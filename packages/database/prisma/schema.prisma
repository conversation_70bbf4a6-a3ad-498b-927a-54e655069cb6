datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

generator client {
    provider = "prisma-client-js"
}

generator zod {
    provider         = "zod-prisma-types"
    output           = "./zod"
    createInputTypes = true
    addIncludeType   = false
    addSelectType    = false
}

generator json {
    provider = "prisma-json-types-generator"
}

enum UserRole {
    CUSTOMER
    SELLER
    ADMIN
    SUPER_ADMIN
}

enum OrderStatus {
    PENDING
    PROCESSING
    COMPLETED
    CANCELLED
    REFUNDED
    FAILED
}

enum OrderType {
    PURCHASE
    SUBSCRIPTION
    UPGRADE
    RENEWAL
}

enum TransactionType {
    CREDIT
    DEBIT
    TRANSFER
    REFUND
    FEE
    COMMISSION
    WITHDRAWAL
    DEPOSIT
    PAYOUT
    CHARGEBACK
    DISPUTE
}

enum TransactionStatus {
    PENDING
    PROCESSING
    COMPLETED
    FAILED
    CANCELLED
    REVERSED
}

enum LedgerEntryType {
    SALE_GROSS
    PLATFORM_FEE
    PAYMENT_PROCESSOR_FEE
    AFFILIATE_COMMISSION_DEBIT
    AFFILIATE_COMMISSION_CREDIT
    COPRODUCER_COMMISSION_DEBIT
    COPRODUCER_COMMISSION_CREDIT
    REFUND
    WITHDRAWAL
    DEPOSIT
    TRANSFER_IN
    TRANSFER_OUT
    ADJUSTMENT
    DISPUTE_FEE
    CHARGEBACK_FEE
    INTERNATIONAL_FEE
}

enum ReportType {
    REVENUE
    COMMISSION
    FEES
    CASH_FLOW
    BALANCE
    TAX
    RECONCILIATION
}

enum ReportPeriod {
    DAILY
    WEEKLY
    MONTHLY
    QUARTERLY
    YEARLY
    CUSTOM
}

enum CashFlowType {
    INFLOW
    OUTFLOW
}

enum CashFlowCategory {
    SALES
    COMMISSIONS
    FEES
    WITHDRAWALS
    REFUNDS
    ADJUSTMENTS
    TRANSFERS
    OTHER
}

model User {
    id                  String       @id @default(cuid())
    name                String?
    email               String       @unique
    emailVerified       Boolean      @default(false)
    image               String?
    createdAt           DateTime     @default(now())
    updatedAt           DateTime     @updatedAt
    username            String?      @unique
    role                UserRole     @default(CUSTOMER)
    banned              Boolean?
    banReason           String?
    banExpires          DateTime?
    onboardingComplete  Boolean      @default(false)
    paymentsCustomerId  String?
    locale              String?
    twoFactorEnabled    Boolean      @default(false)
    twoFactor           TwoFactor[]
    sessions            Session[]
    accounts            Account[]
    passkeys            Passkey[]
    organizationMembers Member[]
    organizationInvites Invitation[]
    purchases           Purchase[]
    aiChats             AiChat[]

    // Digital Products & White-Label
    createdProducts       Product[]              @relation("CreatedProducts")
    orders                Order[]                @relation("OrderBuyer")
    affiliatedOrders      Order[]                @relation("OrderAffiliate")
    enrollments           CourseEnrollment[]
    certificates          Certificate[]
    assets                Asset[]                @relation("CreatedAssets")
    lessonProgress        LessonProgress[]
    affiliateProfile      AffiliateProfile?
    teacherProfile        TeacherProfile?
    ledgerEntries         LedgerEntry[]
    withdraws             Withdraw[]
    reviews               ProductReview[]
    favoriteProducts      Product[]              @relation("FavoriteProducts")
    createdCoupons        Coupon[]               @relation("CreatedCoupons")
    affiliateInvitations  AffiliateInvitation[]  @relation("CreatedAffiliateInvitations")
    teacherInvitations    TeacherInvitation[]    @relation("CreatedTeacherInvitations")
    coProducerInvitations CoProducerInvitation[] @relation("CreatedCoProducerInvitations")
    coProducerRoles       CoProducer[]
    mentoringSessions     MentoringSession[]
    gatewayConfigurations GatewayConfiguration[]
    offerInteractions     OfferInteraction[]
    transactionsFrom      Transaction[]          @relation("TransactionFrom")
    transactionsTo        Transaction[]          @relation("TransactionTo")
    cashFlowEntries       CashFlowEntry[]

    // Community relations
    communityMemberships CommunityMember[]
    communityLikes       CommunityLike[]
    balanceSnapshots     BalanceSnapshot[]

    @@map("user")
}

model Session {
    id        String   @id @default(cuid())
    expiresAt DateTime
    ipAddress String?
    userAgent String?
    userId    String
    user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

    impersonatedBy String?

    activeOrganizationId String?

    token     String
    createdAt DateTime
    updatedAt DateTime

    @@unique([token])
    @@map("session")
}

model Account {
    id           String    @id @default(cuid())
    accountId    String
    providerId   String
    userId       String
    user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    accessToken  String?   @db.Text
    refreshToken String?   @db.Text
    idToken      String?   @db.Text
    expiresAt    DateTime?
    password     String?

    accessTokenExpiresAt  DateTime?
    refreshTokenExpiresAt DateTime?
    scope                 String?
    createdAt             DateTime
    updatedAt             DateTime

    @@map("account")
}

model Verification {
    id         String   @id @default(cuid())
    identifier String
    value      String   @db.Text
    expiresAt  DateTime

    createdAt DateTime?
    updatedAt DateTime?

    @@map("verification")
}

model Passkey {
    id           String    @id @default(cuid())
    name         String?
    publicKey    String
    userId       String
    user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    credentialID String
    counter      Int
    deviceType   String
    backedUp     Boolean
    transports   String?
    createdAt    DateTime?

    @@map("passkey")
}

model TwoFactor {
    id          String @id @default(cuid())
    secret      String
    backupCodes String
    userId      String
    user        User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@map("twoFactor")
}

model Organization {
    id                 String   @id @default(cuid())
    name               String
    slug               String?  @unique
    logo               String?
    createdAt          DateTime @default(now())
    updatedAt          DateTime @updatedAt
    metadata           String?
    paymentsCustomerId String?

    // White-label configuration
    domain             String? @unique
    customDomain       String? @unique
    branding           Json? // Colors, fonts, etc.
    settings           Json? // Platform settings
    subscriptionPlan   String? @default("basic")
    subscriptionStatus String? @default("active")
    billingEmail       String?
    timezone           String? @default("UTC")
    currency           String? @default("BRL")
    language           String? @default("pt")

    // Feature flags
    enableAffiliatePogram  Boolean @default(true)
    enableDigitalProducts  Boolean @default(true)
    enableCertificates     Boolean @default(true)
    enableCustomBranding   Boolean @default(false)
    enableCustomDomain     Boolean @default(false)
    enableAdvancedPayments Boolean @default(false)

    // Limits based on subscription
    maxProducts     Int? @default(10)
    maxUsers        Int? @default(100)
    maxStorageGB    Int? @default(5)
    maxTransactions Int? @default(1000)

    members     Member[]
    invitations Invitation[]
    purchases   Purchase[]
    aiChats     AiChat[]

    // Digital Products & Platform
    products              Product[]
    categories            Category[]
    orders                Order[]
    coupons               Coupon[]
    assets                Asset[]                @relation("OrganizationAssets")
    certificates          Certificate[]
    gatewayConfigurations GatewayConfiguration[]
    paymentProviders      PaymentProvider[]
    webhooks              Webhook[]
    customPages           CustomPage[]
    emailTemplates        EmailTemplate[]
    ledgerEntries         LedgerEntry[]
    transactions          Transaction[]
    financialReports      FinancialReport[]
    cashFlowEntries       CashFlowEntry[]
    balanceSnapshots      BalanceSnapshot[]
    reconciliationEntries ReconciliationEntry[]
    Testimonial           Testimonial[]
    Pixel                 Pixel[]

    // Community relations
    communities Community[]

    // AI Agents
    aiAgents           AiAgent[]
    agentConversations AgentConversation[]
    agentAnalytics     AgentAnalytics[]

    @@map("organization")
}

model Member {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String
    user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
    role           String
    createdAt      DateTime

    @@unique([organizationId, userId])
    @@map("member")
}

model Invitation {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    email          String
    role           String?
    status         String
    expiresAt      DateTime
    inviterId      String
    user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)

    @@map("invitation")
}

enum PurchaseType {
    SUBSCRIPTION
    ONE_TIME
}

model Purchase {
    id             String        @id @default(cuid())
    organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId String?
    user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId         String?
    type           PurchaseType
    customerId     String
    subscriptionId String?       @unique
    productId      String
    status         String?
    createdAt      DateTime      @default(now())
    updatedAt      DateTime      @updatedAt

    @@index([subscriptionId])
    @@map("purchase")
}

model AiChat {
    id             String        @id @default(cuid())
    organizationId String?
    organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String?
    user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
    title          String?
    /// [Array<{role: "user" | "assistant"; content: string;}>]
    messages       Json          @default("[]") /// @zod.custom.use(z.array(z.object({ role: z.enum(['user', 'assistant']), content: z.string() })))
    createdAt      DateTime      @default(now())
    updatedAt      DateTime      @updatedAt

    @@map("ai_chat")
}

// =============================================================================
// DIGITAL PRODUCTS SYSTEM
// =============================================================================

enum ProductType {
    COURSE
    EBOOK
    MENTORSHIP
    SUBSCRIPTION
    BUNDLE
    COMMUNITY
}

enum ProductStatus {
    DRAFT
    PUBLISHED
    ARCHIVED
    SUSPENDED
}

enum ProductVisibility {
    PUBLIC
    PRIVATE
    UNLISTED
}

enum CheckoutType {
    DEFAULT
    CUSTOM
    EXTERNAL
}

model Category {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    name           String
    slug           String
    description    String?
    icon           String?
    color          String?
    sortOrder      Int          @default(0)
    parentId       String?
    parent         Category?    @relation("CategoryParent", fields: [parentId], references: [id])
    children       Category[]   @relation("CategoryParent")
    products       Product[]
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@unique([organizationId, slug])
    @@map("category")
}

model Product {
    id                String            @id @default(cuid())
    organizationId    String
    organization      Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    creatorId         String
    creator           User              @relation("CreatedProducts", fields: [creatorId], references: [id])
    name              String
    slug              String
    description       String?
    shortDescription  String?
    priceCents        Int // Preço em centavos
    comparePriceCents Int? // Preço de comparação em centavos
    currency          String            @default("BRL") // Moeda do produto
    type              ProductType
    status            ProductStatus     @default(DRAFT)
    visibility        ProductVisibility @default(PRIVATE)
    categoryId        String?
    category          Category?         @relation(fields: [categoryId], references: [id])
    thumbnail         String?
    gallery           String[]          @default([])
    tags              String[]          @default([])
    features          String[]          @default([])
    requirements      String[]          @default([])
    duration          Int? // in minutes
    level             String? // BEGINNER, INTERMEDIATE, ADVANCED
    language          String            @default("pt-BR")
    certificate       Boolean           @default(false)
    downloadable      Boolean           @default(false)
    checkoutType      CheckoutType      @default(DEFAULT)
    /// Product settings and metadata
    settings          Json              @default("{}") /// @zod.custom.use(z.record(z.any()))

    // Checkout & Conversion Settings
    guaranteeType    String? @default("30_DAYS") // "NONE", "7_DAYS", "30_DAYS", "60_DAYS", "90_DAYS"
    guaranteeText    String? // Custom guarantee text
    showTestimonials Boolean @default(true)
    showUrgency      Boolean @default(true)
    showScarcity     Boolean @default(true)
    showTrustBadges  Boolean @default(true)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    modules     ProductModule[]
    enrollments CourseEnrollment[]
    reviews     ProductReview[]

    // Product types
    ebook     Ebook?
    course    Course?
    mentoring Mentoring?
    community Community?

    // Sales & Marketing
    offers       Offer[]
    targetOffers Offer[]     @relation("TargetProduct")
    orders       Order[]
    orderItems   OrderItem[]
    favoriteBy   User[]      @relation("FavoriteProducts")

    // Assets & Content
    assets Asset[] @relation("ProductAssets")

    // Affiliates & Co-producers
    affiliateInvitations  AffiliateInvitation[]
    affiliateLinks        AffiliateLink[]
    coProducerInvitations CoProducerInvitation[]
    coProducers           CoProducer[]

    // Abandoned checkouts
    abandonedCheckouts AbandonedCheckout[]

    // Checkout links
    checkoutLinks CheckoutLink[]
    Testimonial   Testimonial[]
    Pixel         Pixel[]

    @@unique([organizationId, slug])
    @@map("product")
}

model ProductModule {
    id          String          @id @default(cuid())
    productId   String
    product     Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
    title       String
    description String?
    order       Int
    duration    Int? // in minutes
    isPublished Boolean         @default(true)
    lessons     ProductLesson[]
    createdAt   DateTime        @default(now())
    updatedAt   DateTime        @updatedAt

    @@map("product_module")
}

model ProductLesson {
    id          String        @id @default(cuid())
    moduleId    String
    module      ProductModule @relation(fields: [moduleId], references: [id], onDelete: Cascade)
    title       String
    description String?
    content     String?       @db.Text
    videoUrl    String?
    duration    Int? // in minutes
    order       Int
    isPublished Boolean       @default(true)
    isFree      Boolean       @default(false)
    /// Additional lesson data
    metadata    Json          @default("{}") /// @zod.custom.use(z.record(z.any()))
    createdAt   DateTime      @default(now())
    updatedAt   DateTime      @updatedAt

    @@map("product_lesson")
}

// ===== ADDITIONAL MODELS FOR DIGITAL PRODUCTS =====

model Order {
    id              String          @id @default(cuid())
    organizationId  String
    organization    Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    buyerId         String
    buyer           User            @relation("OrderBuyer", fields: [buyerId], references: [id])
    affiliateId     String?
    affiliate       User?           @relation("OrderAffiliate", fields: [affiliateId], references: [id])
    productId       String
    product         Product         @relation(fields: [productId], references: [id])
    status          OrderStatus     @default(PENDING)
    type            OrderType       @default(PURCHASE)
    totalCents      Int // Valor total em centavos
    currency        String          @default("BRL")
    accountCurrency String? // Moeda da conta do usuário
    paymentMethod   String?
    paymentId       String?
    createdAt       DateTime        @default(now())
    updatedAt       DateTime        @updatedAt
    items           OrderItem[]
    ledgerEntries   LedgerEntry[]
    transactions    Transaction[]
    cashFlowEntries CashFlowEntry[]

    @@map("orders")
}

model OrderItem {
    id         String  @id @default(cuid())
    orderId    String
    order      Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
    productId  String
    product    Product @relation(fields: [productId], references: [id])
    quantity   Int     @default(1)
    priceCents Int // Preço unitário em centavos
    totalCents Int // Total em centavos

    @@map("order_items")
}

model Certificate {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String
    user           User         @relation(fields: [userId], references: [id])
    productId      String?
    title          String
    description    String?
    certificateUrl String?
    issuedAt       DateTime     @default(now())
    expiresAt      DateTime?
    metadata       Json? /// @zod.custom.use(z.record(z.any()).optional())

    @@map("certificates")
}

model Asset {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation("OrganizationAssets", fields: [organizationId], references: [id], onDelete: Cascade)
    creatorId      String
    creator        User         @relation("CreatedAssets", fields: [creatorId], references: [id])
    productId      String?
    product        Product?     @relation("ProductAssets", fields: [productId], references: [id])
    name           String
    type           String
    url            String
    size           Int?
    metadata       Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@map("assets")
}

model LessonProgress {
    id        String   @id @default(cuid())
    userId    String
    user      User     @relation(fields: [userId], references: [id])
    lessonId  String
    completed Boolean  @default(false)
    progress  Int      @default(0)
    watchTime Int      @default(0)
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([userId, lessonId])
    @@map("lesson_progress")
}

model AffiliateProfile {
    id                 String   @id @default(cuid())
    userId             String   @unique
    user               User     @relation(fields: [userId], references: [id])
    commissionCents    Int      @default(0) // Comissão em centavos
    totalEarningsCents Int      @default(0) // Total de ganhos em centavos
    currency           String   @default("BRL") // Moeda da conta do afiliado
    isActive           Boolean  @default(true)
    bankAccount        Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt          DateTime @default(now())
    updatedAt          DateTime @updatedAt

    @@map("affiliate_profiles")
}

model TeacherProfile {
    id          String   @id @default(cuid())
    userId      String   @unique
    user        User     @relation(fields: [userId], references: [id])
    bio         String?
    expertise   String[] @default([])
    experience  String?
    education   String?
    website     String?
    socialLinks Json? /// @zod.custom.use(z.record(z.any()).optional())
    isVerified  Boolean  @default(false)
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    @@map("teacher_profiles")
}

model LedgerEntry {
    id              String          @id @default(cuid())
    organizationId  String
    organization    Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId          String
    user            User            @relation(fields: [userId], references: [id])
    type            LedgerEntryType
    amountCents     Int // Valor em centavos
    currency        String          @default("BRL")
    accountCurrency String? // Moeda da conta do usuário
    description     String?
    orderId         String?
    order           Order?          @relation(fields: [orderId], references: [id])
    transactionId   String?
    transaction     Transaction?    @relation(fields: [transactionId], references: [id])
    referenceId     String?
    availableAt     DateTime?
    settledAt       DateTime? // Data de liquidação
    metadata        Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt       DateTime        @default(now())
    updatedAt       DateTime        @updatedAt

    @@map("ledger_entries")
}

model Withdraw {
    id              String    @id @default(cuid())
    userId          String
    user            User      @relation(fields: [userId], references: [id])
    amountCents     Int // Valor em centavos
    currency        String    @default("BRL")
    accountCurrency String? // Moeda da conta do usuário
    status          String    @default("pending")
    method          String
    feeCents        Int? // Taxa em centavos
    netAmountCents  Int? // Valor líquido em centavos
    externalId      String? // ID do gateway de pagamento
    processedAt     DateTime?
    settledAt       DateTime?
    details         Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt       DateTime  @default(now())
    updatedAt       DateTime  @updatedAt

    @@map("withdraws")
}

model Coupon {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    creatorId      String
    creator        User         @relation("CreatedCoupons", fields: [creatorId], references: [id])
    code           String
    type           String
    valueCents     Int // Valor do cupom em centavos
    minAmountCents Int? // Valor mínimo em centavos
    currency       String       @default("BRL") // Moeda do cupom
    maxUses        Int?
    usedCount      Int          @default(0)
    isActive       Boolean      @default(true)
    startsAt       DateTime?
    expiresAt      DateTime?
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@unique([organizationId, code])
    @@map("coupons")
}

model CourseEnrollment {
    id          String    @id @default(cuid())
    userId      String
    user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    productId   String
    product     Product   @relation(fields: [productId], references: [id], onDelete: Cascade)
    enrolledAt  DateTime  @default(now())
    completedAt DateTime?
    progress    Int       @default(0) // percentage 0-100

    @@unique([userId, productId])
    @@map("course_enrollment")
}

model ProductReview {
    id        String   @id @default(cuid())
    userId    String
    user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    productId String
    product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
    rating    Int // 1-5 stars
    comment   String?
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([userId, productId])
    @@map("product_review")
}

model Testimonial {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

    // Customer information
    customerName     String
    customerEmail    String?
    customerPhoto    String?
    customerRole     String? // e.g., "Empresário", "Estudante"
    customerLocation String? // e.g., "São Paulo, SP"

    // Testimonial content
    title   String?
    content String
    rating  Int // 1-5 stars

    // Source attribution
    source     String // "WHATSAPP", "FACEBOOK", "TIKTOK", "INSTAGRAM", "EMAIL", "MANUAL"
    sourceUrl  String? // Link to original post/message
    sourceDate DateTime? // When the testimonial was originally posted

    // Media attachments
    attachments Json @default("[]") /// @zod.custom.use(z.array(z.string()))

    // Admin management
    isApproved Boolean   @default(false)
    isActive   Boolean   @default(true)
    isFeatured Boolean   @default(false)
    approvedBy String?
    approvedAt DateTime?

    // Product association (optional - can be general testimonials)
    productId String?
    product   Product? @relation(fields: [productId], references: [id], onDelete: SetNull)

    // Metadata
    tags     Json @default("[]") /// @zod.custom.use(z.array(z.string()))
    metadata Json @default("{}") /// @zod.custom.use(z.record(z.any()))

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("testimonial")
}

// ===== REMAINING MODELS =====

model AffiliateInvitation {
    id              String    @id @default(cuid())
    creatorId       String
    creator         User      @relation("CreatedAffiliateInvitations", fields: [creatorId], references: [id])
    productId       String
    product         Product   @relation(fields: [productId], references: [id])
    email           String
    commissionCents Int // Comissão em centavos
    currency        String    @default("BRL") // Moeda da comissão
    status          String    @default("pending")
    expiresAt       DateTime?
    createdAt       DateTime  @default(now())
    updatedAt       DateTime  @updatedAt

    @@map("affiliate_invitations")
}

model TeacherInvitation {
    id        String    @id @default(cuid())
    creatorId String
    creator   User      @relation("CreatedTeacherInvitations", fields: [creatorId], references: [id])
    email     String
    role      String    @default("teacher")
    status    String    @default("pending")
    expiresAt DateTime?
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt

    @@map("teacher_invitations")
}

model CoProducerInvitation {
    id                    String    @id @default(cuid())
    creatorId             String
    creator               User      @relation("CreatedCoProducerInvitations", fields: [creatorId], references: [id])
    productId             String
    product               Product   @relation(fields: [productId], references: [id])
    email                 String
    percentageBasisPoints Int // Porcentagem em basis points (ex: 500 = 5.00%)
    status                String    @default("pending")
    expiresAt             DateTime?
    createdAt             DateTime  @default(now())
    updatedAt             DateTime  @updatedAt

    @@map("coproducer_invitations")
}

model CoProducer {
    id                    String   @id @default(cuid())
    userId                String
    user                  User     @relation(fields: [userId], references: [id])
    productId             String
    product               Product  @relation(fields: [productId], references: [id])
    percentageBasisPoints Int // Porcentagem em basis points (ex: 500 = 5.00%)
    isActive              Boolean  @default(true)
    createdAt             DateTime @default(now())
    updatedAt             DateTime @updatedAt

    @@unique([userId, productId])
    @@map("coproducers")
}

model MentoringSession {
    id          String   @id @default(cuid())
    userId      String
    user        User     @relation(fields: [userId], references: [id])
    mentorId    String
    title       String
    description String?
    scheduledAt DateTime
    duration    Int      @default(60)
    status      String   @default("scheduled")
    meetingUrl  String?
    notes       String?
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    @@map("mentoring_sessions")
}

model GatewayConfiguration {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String
    user           User         @relation(fields: [userId], references: [id])
    name           String
    type           String
    settings       Json /// @zod.custom.use(z.record(z.any()))
    isActive       Boolean      @default(true)
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@map("gateway_configurations")
}

model PaymentProvider {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    name           String
    type           String
    version        String       @default("1.0.0")
    settings       Json /// @zod.custom.use(z.record(z.any()))
    isActive       Boolean      @default(true)
    priority       Int          @default(0) // Para fallback
    maxRetries     Int          @default(3)
    timeout        Int          @default(30000) // em ms

    // Health check e métricas
    lastHealthCheck     DateTime?
    isHealthy           Boolean   @default(true)
    healthCheckInterval Int       @default(5) // em minutos
    lastError           String?
    errorCount          Int       @default(0)

    // Métricas de performance
    totalTransactions      Int       @default(0)
    successfulTransactions Int       @default(0)
    failedTransactions     Int       @default(0)
    averageResponseTime    Int       @default(0) // em ms
    lastTransactionAt      DateTime?

    // Rate limiting
    rateLimitRequests Int? // requests por janela
    rateLimitWindow   Int? // janela em segundos

    // Fallback providers
    fallbackProviders Json? /// @zod.custom.use(z.array(z.string()).optional())

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relacionamentos
    transactions Transaction[] @relation("ProviderTransactions")

    @@index([organizationId, type])
    @@index([organizationId, isActive])
    @@index([organizationId, priority])
    @@map("payment_providers")
}

model Webhook {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    url            String
    events         String[]     @default([])
    secret         String?
    isActive       Boolean      @default(true)
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@map("webhooks")
}

model CustomPage {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    title          String
    slug           String
    content        String?      @db.Text
    isPublished    Boolean      @default(false)
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@unique([organizationId, slug])
    @@map("custom_pages")
}

model EmailTemplate {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    name           String
    subject        String
    content        String       @db.Text
    variables      String[]     @default([])
    isActive       Boolean      @default(true)
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@unique([organizationId, name])
    @@map("email_templates")
}

model Ebook {
    id        String   @id @default(cuid())
    productId String   @unique
    product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
    fileUrl   String
    fileSize  Int?
    pages     Int?
    format    String   @default("PDF")
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("ebooks")
}

model Course {
    id            String   @id @default(cuid())
    productId     String   @unique
    product       Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
    totalLessons  Int      @default(0)
    totalDuration Int      @default(0)
    level         String   @default("BEGINNER")
    certificate   Boolean  @default(false)
    createdAt     DateTime @default(now())
    updatedAt     DateTime @updatedAt

    @@map("courses")
}

model Mentoring {
    id              String   @id @default(cuid())
    productId       String   @unique
    product         Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
    sessionDuration Int      @default(60)
    maxSessions     Int?
    isRecurring     Boolean  @default(false)
    createdAt       DateTime @default(now())
    updatedAt       DateTime @updatedAt

    @@map("mentorings")
}

model Offer {
    id              String             @id @default(cuid())
    productId       String
    product         Product            @relation(fields: [productId], references: [id])
    targetProductId String?
    targetProduct   Product?           @relation("TargetProduct", fields: [targetProductId], references: [id])
    name            String
    type            String
    valueCents      Int // Valor da oferta em centavos
    currency        String             @default("BRL") // Moeda da oferta
    isActive        Boolean            @default(true)
    startsAt        DateTime?
    expiresAt       DateTime?
    createdAt       DateTime           @default(now())
    updatedAt       DateTime           @updatedAt
    interactions    OfferInteraction[]

    @@map("offers")
}

model Pixel {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    productId      String?
    product        Product?     @relation(fields: [productId], references: [id], onDelete: Cascade)
    name           String
    platform       String // "facebook", "google", "tiktok", "custom"
    pixelId        String
    events         String[]     @default([])
    isActive       Boolean      @default(true)
    settings       Json?
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@map("pixels")
}

model AffiliateLink {
    id            String   @id @default(cuid())
    productId     String
    product       Product  @relation(fields: [productId], references: [id])
    code          String   @unique
    clicks        Int      @default(0)
    sales         Int      @default(0)
    earningsCents Int      @default(0) // Ganhos em centavos
    currency      String   @default("BRL") // Moeda dos ganhos
    isActive      Boolean  @default(true)
    createdAt     DateTime @default(now())
    updatedAt     DateTime @updatedAt

    @@map("affiliate_links")
}

model AbandonedCheckout {
    id        String   @id @default(cuid())
    productId String
    product   Product  @relation(fields: [productId], references: [id])
    email     String
    data      Json /// @zod.custom.use(z.record(z.any()))
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("abandoned_checkouts")
}

model CheckoutLink {
    id               String                   @id @default(cuid())
    productId        String
    product          Product                  @relation(fields: [productId], references: [id], onDelete: Cascade)
    offerId          String?
    url              String
    expiresAt        DateTime?
    utmParams        String? // JSON string
    customParams     String? // JSON string
    clickCount       Int                      @default(0)
    conversionCount  Int                      @default(0)
    totalRevenue     Int                      @default(0) // in cents
    lastClickedAt    DateTime?
    lastConversionAt DateTime?
    createdAt        DateTime                 @default(now())
    updatedAt        DateTime                 @updatedAt
    clicks           CheckoutLinkClick[]
    conversions      CheckoutLinkConversion[]

    @@map("checkout_links")
}

model CheckoutLinkClick {
    id        String       @id @default(cuid())
    linkId    String
    link      CheckoutLink @relation(fields: [linkId], references: [id], onDelete: Cascade)
    userAgent String?
    ipAddress String?
    referrer  String?
    utmParams String? // JSON string
    clickedAt DateTime     @default(now())

    @@map("checkout_link_clicks")
}

model CheckoutLinkConversion {
    id            String       @id @default(cuid())
    linkId        String
    link          CheckoutLink @relation(fields: [linkId], references: [id], onDelete: Cascade)
    orderId       String
    amount        Int // in cents
    paymentMethod String
    convertedAt   DateTime     @default(now())

    @@map("checkout_link_conversions")
}

model OfferInteraction {
    id        String   @id @default(cuid())
    offerId   String
    offer     Offer    @relation(fields: [offerId], references: [id], onDelete: Cascade)
    userId    String?
    user      User?    @relation(fields: [userId], references: [id])
    type      String
    metadata  Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt DateTime @default(now())

    @@map("offer_interactions")
}

model Transaction {
    id                String            @id @default(cuid())
    organizationId    String
    organization      Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    type              TransactionType
    status            TransactionStatus @default(PENDING)
    amountCents       Int // Valor em centavos
    currency          String            @default("BRL")
    accountCurrency   String? // Moeda da conta do usuário
    description       String?
    externalId        String? // ID do gateway de pagamento
    gatewayId         String?
    paymentMethod     String?
    fromUserId        String?
    fromUser          User?             @relation("TransactionFrom", fields: [fromUserId], references: [id])
    toUserId          String?
    toUser            User?             @relation("TransactionTo", fields: [toUserId], references: [id])
    orderId           String?
    order             Order?            @relation(fields: [orderId], references: [id])
    parentId          String?
    parent            Transaction?      @relation("TransactionParent", fields: [parentId], references: [id])
    children          Transaction[]     @relation("TransactionParent")
    ledgerEntries     LedgerEntry[]
    fees              TransactionFee[]
    cashFlowEntries   CashFlowEntry[]
    metadata          Json? /// @zod.custom.use(z.record(z.any()).optional())
    processedAt       DateTime?
    settledAt         DateTime?
    createdAt         DateTime          @default(now())
    updatedAt         DateTime          @updatedAt
    PaymentProvider   PaymentProvider?  @relation("ProviderTransactions", fields: [paymentProviderId], references: [id])
    paymentProviderId String?

    @@map("transactions")
}

model TransactionFee {
    id            String      @id @default(cuid())
    transactionId String
    transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
    type          String // PLATFORM, PAYMENT_PROCESSOR, INTERNATIONAL, etc.
    amountCents   Int // Valor da taxa em centavos
    percentage    Decimal?    @db.Decimal(5, 4)
    description   String?
    createdAt     DateTime    @default(now())

    @@map("transaction_fees")
}

model FinancialReport {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    type           ReportType
    period         ReportPeriod
    startDate      DateTime
    endDate        DateTime
    data           Json /// @zod.custom.use(z.record(z.any()))
    summary        Json? /// @zod.custom.use(z.record(z.any()).optional())
    generatedBy    String?
    generatedAt    DateTime     @default(now())
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@map("financial_reports")
}

model CashFlowEntry {
    id             String           @id @default(cuid())
    organizationId String
    organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    type           CashFlowType
    category       CashFlowCategory
    amountCents    Int // Valor em centavos
    currency       String           @default("BRL")
    description    String?
    transactionId  String?
    transaction    Transaction?     @relation(fields: [transactionId], references: [id])
    orderId        String?
    order          Order?           @relation(fields: [orderId], references: [id])
    userId         String?
    user           User?            @relation(fields: [userId], references: [id])
    date           DateTime
    metadata       Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt      DateTime         @default(now())
    updatedAt      DateTime         @updatedAt

    @@map("cash_flow_entries")
}

model BalanceSnapshot {
    id                    String       @id @default(cuid())
    organizationId        String
    organization          Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId                String?
    user                  User?        @relation(fields: [userId], references: [id])
    totalBalanceCents     Int // Saldo total em centavos
    availableBalanceCents Int // Saldo disponível em centavos
    pendingBalanceCents   Int // Saldo pendente em centavos
    reservedBalanceCents  Int // Saldo reservado em centavos
    currency              String       @default("BRL")
    accountCurrency       String? // Moeda da conta do usuário
    snapshotDate          DateTime
    metadata              Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt             DateTime     @default(now())

    @@map("balance_snapshots")
}

model ReconciliationEntry {
    id                 String       @id @default(cuid())
    organizationId     String
    organization       Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    externalId         String
    externalSource     String
    internalId         String?
    amountCents        Int // Valor em centavos
    currency           String       @default("BRL")
    status             String       @default("PENDING")
    discrepancyCents   Int? // Discrepância em centavos
    reconciliationDate DateTime
    resolvedAt         DateTime?
    resolvedBy         String?
    notes              String?
    metadata           Json? /// @zod.custom.use(z.record(z.any()).optional())
    createdAt          DateTime     @default(now())
    updatedAt          DateTime     @updatedAt

    @@map("reconciliation_entries")
}

// =============================================================================
// AI AGENTS SYSTEM
// =============================================================================

enum AgentType {
    SALES
    SUPPORT
    ONBOARDING
    COMMUNITY
}

enum AgentStatus {
    ACTIVE
    INACTIVE
    PAUSED
    TRAINING
}

enum AgentPersonality {
    FRIENDLY
    PROFESSIONAL
    ENTHUSIASTIC
    TECHNICAL
    CASUAL
}

enum IntegrationType {
    WHATSAPP
    TELEGRAM
    CHAT_WIDGET
    EMAIL
    CRM
}

model AiAgent {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    name           String
    description    String?
    type           AgentType
    status         AgentStatus  @default(INACTIVE)

    // Personality and behavior
    personality AgentPersonality @default(FRIENDLY)
    tone        String? // Custom tone description
    language    String           @default("pt-BR")

    // Configuration
    systemPrompt    String? @db.Text // System instructions for the AI
    welcomeMessage  String? // Initial message when conversation starts
    fallbackMessage String? // Message when AI can't respond

    // Training data
    trainingData  Json? /// @zod.custom.use(z.record(z.any()).optional()) // Custom training data
    knowledgeBase Json? /// @zod.custom.use(z.record(z.any()).optional()) // Knowledge base documents

    // Goals and metrics
    conversionGoal   Float? // Target conversion rate (0-1)
    leadGoal         Int? // Target number of leads per month
    responseTimeGoal Int? // Target response time in seconds

    // Settings
    isPublic        Boolean @default(false) // Can be used by other organizations
    allowEscalation Boolean @default(true) // Allow escalation to human
    maxInteractions Int? // Maximum interactions per conversation

    // Analytics
    totalInteractions Int    @default(0)
    totalLeads        Int    @default(0)
    totalConversions  Int    @default(0)
    avgResponseTime   Float? // Average response time in seconds
    satisfactionScore Float? // Average satisfaction score (0-5)

    // Timestamps
    lastActiveAt DateTime?
    createdAt    DateTime  @default(now())
    updatedAt    DateTime  @updatedAt

    // Relations
    integrations  AgentIntegration[]
    conversations AgentConversation[]
    templates     AgentTemplate[]
    analytics     AgentAnalytics[]

    @@map("ai_agents")
}

model AgentIntegration {
    id        String          @id @default(cuid())
    agentId   String
    agent     AiAgent         @relation(fields: [agentId], references: [id], onDelete: Cascade)
    type      IntegrationType
    name      String
    settings  Json /// @zod.custom.use(z.record(z.any())) // Integration-specific settings
    isActive  Boolean         @default(true)
    createdAt DateTime        @default(now())
    updatedAt DateTime        @updatedAt

    @@map("agent_integrations")
}

model AgentConversation {
    id             String       @id @default(cuid())
    agentId        String
    agent          AiAgent      @relation(fields: [agentId], references: [id], onDelete: Cascade)
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

    // Customer information
    customerId    String? // If customer is registered
    customerName  String?
    customerEmail String?
    customerPhone String?

    // Conversation data
    messages Json /// @zod.custom.use(z.array(z.object({ role: z.enum(['user', 'assistant', 'system']), content: z.string(), timestamp: z.string() })))
    status   String @default("active") // active, completed, escalated, abandoned
    channel  String // whatsapp, telegram, chat, email

    // Analytics
    interactionCount    Int     @default(0)
    leadGenerated       Boolean @default(false)
    conversionGenerated Boolean @default(false)
    satisfactionRating  Int? // 1-5 rating
    escalatedToHuman    Boolean @default(false)

    // Timestamps
    startedAt     DateTime  @default(now())
    lastMessageAt DateTime  @default(now())
    completedAt   DateTime?

    @@map("agent_conversations")
}

model AgentTemplate {
    id          String   @id @default(cuid())
    agentId     String
    agent       AiAgent  @relation(fields: [agentId], references: [id], onDelete: Cascade)
    name        String
    description String?
    type        String // question, response, escalation, etc.
    content     String   @db.Text
    isActive    Boolean  @default(true)
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    @@map("agent_templates")
}

model AgentAnalytics {
    id             String       @id @default(cuid())
    agentId        String
    agent          AiAgent      @relation(fields: [agentId], references: [id], onDelete: Cascade)
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

    // Date range
    date   DateTime
    period String // daily, weekly, monthly

    // Metrics
    totalInteractions Int    @default(0)
    totalLeads        Int    @default(0)
    totalConversions  Int    @default(0)
    avgResponseTime   Float?
    satisfactionScore Float?
    escalationRate    Float?

    // Channel breakdown
    whatsappInteractions Int @default(0)
    telegramInteractions Int @default(0)
    chatInteractions     Int @default(0)
    emailInteractions    Int @default(0)

    createdAt DateTime @default(now())

    @@unique([agentId, date, period])
    @@map("agent_analytics")
}

// =============================================================================
// COMMUNITY SYSTEM
// =============================================================================

model Community {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    productId      String?      @unique
    product        Product?     @relation(fields: [productId], references: [id], onDelete: Cascade)

    // Basic info
    name             String
    slug             String
    description      String?
    shortDescription String?
    thumbnail        String?
    banner           String?

    // Community settings
    isPublic    Boolean @default(false)
    isActive    Boolean @default(true)
    memberCount Int     @default(0)
    maxMembers  Int?

    // Pricing
    priceCents  Int? // null = free community
    currency    String @default("BRL")
    billingType String @default("ONE_TIME") // ONE_TIME, MONTHLY, YEARLY

    // Features
    features String[] @default([])
    tags     String[] @default([])
    language String   @default("pt-BR")

    // Access control
    accessType String  @default("PAID") // FREE, PAID, INVITE_ONLY
    inviteCode String?

    // Community platform integration
    platformType String? // DISCORD, TELEGRAM, WHATSAPP, CUSTOM
    platformUrl  String?
    platformId   String?

    // Settings
    settings Json @default("{}")

    // Timestamps
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    members    CommunityMember[]
    posts      CommunityPost[]
    categories CommunityCategory[]

    @@unique([organizationId, slug])
    @@map("community")
}

model CommunityMember {
    id          String    @id @default(cuid())
    communityId String
    community   Community @relation(fields: [communityId], references: [id], onDelete: Cascade)
    userId      String
    user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

    // Member info
    role   String @default("MEMBER") // MEMBER, MODERATOR, ADMIN, OWNER
    status String @default("ACTIVE") // ACTIVE, INACTIVE, BANNED, PENDING

    // Access
    joinedAt     DateTime  @default(now())
    lastActiveAt DateTime?
    expiresAt    DateTime? // For paid communities

    // Relations
    posts            CommunityPost[]
    CommunityComment CommunityComment[]

    @@unique([communityId, userId])
    @@map("community_member")
}

model CommunityPost {
    id          String             @id @default(cuid())
    communityId String
    community   Community          @relation(fields: [communityId], references: [id], onDelete: Cascade)
    authorId    String
    author      CommunityMember    @relation(fields: [authorId], references: [id], onDelete: Cascade)
    categoryId  String?
    category    CommunityCategory? @relation(fields: [categoryId], references: [id])

    // Post content
    title    String
    content  String  @db.Text
    type     String  @default("POST") // POST, ANNOUNCEMENT, POLL, EVENT
    isPinned Boolean @default(false)
    isLocked Boolean @default(false)

    // Engagement
    likesCount    Int @default(0)
    commentsCount Int @default(0)
    viewsCount    Int @default(0)

    // Status
    status String @default("PUBLISHED") // DRAFT, PUBLISHED, ARCHIVED, DELETED

    // Timestamps
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    comments CommunityComment[]
    likes    CommunityLike[]

    @@map("community_post")
}

model CommunityCategory {
    id          String    @id @default(cuid())
    communityId String
    community   Community @relation(fields: [communityId], references: [id], onDelete: Cascade)

    name        String
    description String?
    color       String?
    order       Int     @default(0)
    isActive    Boolean @default(true)

    // Timestamps
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    posts CommunityPost[]

    @@map("community_category")
}

model CommunityComment {
    id       String          @id @default(cuid())
    postId   String
    post     CommunityPost   @relation(fields: [postId], references: [id], onDelete: Cascade)
    authorId String
    author   CommunityMember @relation(fields: [authorId], references: [id], onDelete: Cascade)

    content String @db.Text
    status  String @default("PUBLISHED") // PUBLISHED, DELETED

    // Timestamps
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("community_comment")
}

model CommunityLike {
    id     String        @id @default(cuid())
    postId String
    post   CommunityPost @relation(fields: [postId], references: [id], onDelete: Cascade)
    userId String
    user   User          @relation(fields: [userId], references: [id], onDelete: Cascade)

    createdAt DateTime @default(now())

    @@unique([postId, userId])
    @@map("community_like")
}
