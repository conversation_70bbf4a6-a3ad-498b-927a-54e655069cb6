import { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import { db } from "@repo/database";
import { checkoutFormSchema, type CheckoutFormData } from "../schema";
import { getStripeClient } from "@repo/payments";
import { OrderStatus, OrderType } from "@prisma/client";
import crypto from 'crypto';

// Função para gerar uma senha temporária
function generateTemporaryPassword() {
  return crypto.randomBytes(4).toString('hex');
}

export async function processPayment(c: Context, input: CheckoutFormData) {
  try {
    // 1. Validar produto
    const product = await db.product.findUnique({
      where: {
        id: input.productId,
        status: 'PUBLISHED'
      },
      include: {
        offers: {
          where: {
            isActive: true,
            OR: [
              {
                AND: [
                  { id: { in: input.orderBumpIds || [] } },
                  { type: 'ORDER_BUMP' },
                ],
              },
              ...(input.offerId ? [{ id: input.offerId }] : []),
            ],
          },
        },
      },
    });

    if (!product) {
      console.error("[PAYMENT_ERROR] Product not found:", input.productId);
      throw new HTTPException(404, { message: 'Produto não encontrado' });
    }

    // For now, accept all payment methods since we don't have this field in the schema
    // This can be configured later in the product settings

    // 2. Calcular valor total
    let totalAmount = Number(product.priceCents) / 100;

    // If a specific offer is selected, use its price instead of the product price
    if (input.offerId) {
      const selectedOffer = product.offers.find(offer => offer.id === input.offerId);
      if (selectedOffer && selectedOffer.type !== 'ORDER_BUMP') {
        totalAmount = Number(selectedOffer.valueCents) / 100;
      }
    }

    // Add order bumps to the total
    if (input?.orderBumpIds && input.orderBumpIds.length > 0) {
      const orderBumpsTotal = product.offers
        .filter(offer => offer.type === 'ORDER_BUMP' && input.orderBumpIds!.includes(offer.id))
        .reduce((acc: any, offer) => acc + (Number(offer.valueCents) / 100), 0);
      totalAmount += orderBumpsTotal;
    }

    // 3. Aplicar cupom de desconto se fornecido
    let discountAmount = 0;
    let appliedCoupon = null;

    if (input.couponCode) {
      const coupon = await db.coupon.findFirst({
        where: {
          code: input.couponCode,
          organizationId: product.organizationId,
          isActive: true,
          OR: [
            { startsAt: null },
            { startsAt: { lte: new Date() } },
          ],
          AND: [
            {
              OR: [
                { expiresAt: null },
                { expiresAt: { gte: new Date() } },
              ],
            },
          ],
        },
      });

      if (coupon) {
        // Verificar se ainda pode ser usado
        if (coupon.maxUses && coupon.usedCount >= coupon.maxUses) {
          throw new HTTPException(400, { message: "Cupom esgotado" });
        }

        // Verificar valor mínimo
        if (coupon.minAmountCents && totalAmount * 100 < coupon.minAmountCents) {
          throw new HTTPException(400, {
            message: `Valor mínimo para este cupom é R$ ${(coupon.minAmountCents / 100).toFixed(2)}`
          });
        }

        // Calcular desconto
        if (coupon.type === "PERCENTAGE") {
          discountAmount = (totalAmount * coupon.valueCents) / 10000;
        } else {
          discountAmount = coupon.valueCents / 100;
        }

        // Garantir que o desconto não seja maior que o valor total
        discountAmount = Math.min(discountAmount, totalAmount);
        appliedCoupon = coupon;

        // Incrementar contador de uso do cupom
        await db.coupon.update({
          where: { id: coupon.id },
          data: { usedCount: { increment: 1 } },
        });
      } else {
        throw new HTTPException(400, { message: "Cupom inválido ou expirado" });
      }
    }

    const finalAmount = totalAmount - discountAmount;


    console.log("[PAYMENT_DEBUG] Price calculation:", {
      basePrice: Number(product.priceCents) / 100,
      offersTotal: input?.orderBumpIds?.length > 0 ? totalAmount - (Number(product.priceCents) / 100) + discountAmount : 0,
      discountAmount,
      finalTotal: finalAmount
    });

    // 3. Verificar se o usuário existe ou criar uma nova conta
    let userAccount = null;
    let isNewUser = false;
    let tempPassword = null;

    // Verificar se existe um usuário com o email fornecido
    userAccount = await db.user.findUnique({
      where: { email: input.customerData.email }
    });

    // Se ainda não existir, criar uma nova conta
    if (!userAccount) {
      isNewUser = true;
      tempPassword = generateTemporaryPassword();

      // Criar o usuário no banco de dados
      userAccount = await db.user.create({
        data: {
          email: input.customerData.email,
          name: input.customerData.name,
          emailVerified: false,
        }
      });

      console.log('[PAYMENT_DEBUG] Created new user account:', userAccount.id);
    }

    // 4. Criar ordem no banco
    console.log("[PAYMENT_DEBUG] Creating order");

    // Primeiro, verificar se o usuário tem uma organização ou criar uma
    let organizationId = null;
    const userOrganizations = await db.member.findFirst({
      where: { userId: userAccount.id },
      include: { organization: true }
    });

    if (userOrganizations) {
      organizationId = userOrganizations.organizationId;
    } else {
      // Criar uma organização padrão para o usuário
      const organization = await db.organization.create({
        data: {
          name: `${userAccount.name}'s Organization`,
          slug: `${userAccount.id}-org`,
          members: {
            create: {
              userId: userAccount.id,
              role: 'OWNER',
              createdAt: new Date()
            }
          }
        }
      });
      organizationId = organization.id;
    }

    const orderData = {
      organizationId,
      buyerId: userAccount.id,
      productId: product.id,
      totalCents: Math.round(finalAmount * 100), // Converter para centavos
      status: OrderStatus.PENDING,
      type: OrderType.PURCHASE,
      paymentMethod: input.paymentMethod,
    };

    console.log("[PAYMENT_DEBUG] Order data:", {
      ...orderData,
      paymentData: "HIDDEN"
    });

    const order = await db.order.create({
      data: orderData
    });

    // Create order items for selected order bumps only
    if (input?.orderBumpIds?.length > 0) {
      // Filter offers to only include selected order bumps
      const selectedOffers = product.offers.filter(
        (offer) => offer.type === 'ORDER_BUMP' && input.orderBumpIds!.includes(offer.id)
      );

      if (selectedOffers.length > 0) {
        const orderItems = selectedOffers.map((offer) => ({
          orderId: order.id,
          productId: offer.targetProductId || product.id,
          quantity: 1,
          priceCents: Number(offer.valueCents),
          totalCents: Number(offer.valueCents),
        }));

        await db.orderItem.createMany({
          data: orderItems,
        });

        console.log("[PAYMENT_DEBUG] Created order items for selected order bumps:", orderItems.length);
      }
    }

    // 5. Criar checkout session no Stripe
    const stripe = getStripeClient();

    const session = await stripe.checkout.sessions.create({
      payment_method_types: input.paymentMethod === 'CREDIT_CARD' ? ['card'] : ['pix'],
      line_items: [
        {
          price_data: {
            currency: 'brl',
            product_data: {
              name: product.name,
              description: product.description || undefined,
            },
            unit_amount: Math.round(finalAmount * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/checkout/success?orderId=${order.id}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/checkout/cancel?orderId=${order.id}`,
      customer_email: input.customerData.email,
      metadata: {
        orderId: order.id,
        userId: userAccount.id,
        productId: product.id,
      },
    });

    // 6. Atualizar ordem com ID do gateway
    console.log("[PAYMENT_DEBUG] Updating order with payment info");
    const updatedOrder = await db.order.update({
      where: { id: order.id },
      data: {
        paymentId: session.id,
      },
    });

    console.log("[PAYMENT_SUCCESS] Order processed:", {
      orderId: updatedOrder.id,
      status: updatedOrder.status
    });

    return c.json({
      orderId: order.id,
      checkoutUrl: session.url,
      paymentMethod: input.paymentMethod,
      isNewUser,
      loginLink: isNewUser ? `${process.env.NEXT_PUBLIC_APP_URL}/login?email=${encodeURIComponent(userAccount.email)}` : undefined
    });

  } catch (error) {
    console.error("[PAYMENT_ERROR] Full error:", error);

    throw new HTTPException(500, {
      message: error instanceof Error ? error.message : 'Erro ao processar pagamento'
    });
  }
}
