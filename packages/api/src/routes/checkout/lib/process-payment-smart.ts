import { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import { db } from "@repo/database";
import { checkoutFormSchema, type CheckoutFormData } from "../schema";
import { processPayment } from "./process-payment";
import { processPaymentWithCelcoin } from "./process-payment-celcoin";

export async function processPaymentSmart(c: Context, input: CheckoutFormData) {
  try {
    console.log("[PAYMENT_SMART] Starting smart payment processing...");

    // 1. Buscar produto para obter organização
    const product = await db.product.findUnique({
      where: {
        id: input.productId,
        status: 'PUBLISHED'
      }
    });

    if (!product) {
      throw new HTTPException(404, { message: 'Produto não encontrado' });
    }

    // 2. Verificar qual payment provider está ativo para esta organização
    const activeProvider = await db.paymentProvider.findFirst({
      where: {
        organizationId: product.organizationId,
        isActive: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log("[PAYMENT_SMART] Active provider:", activeProvider?.type || "default");

    // 3. Escolher o processamento baseado no provider ativo
    if (activeProvider?.type === 'celcoin') {
      console.log("[PAYMENT_SMART] Using Celcoin processing");
      return await processPaymentWithCelcoin(c, input);
    } else {
      console.log("[PAYMENT_SMART] Using default Stripe processing");
      return await processPayment(c, input);
    }

  } catch (error) {
    console.error("[PAYMENT_SMART] Error:", error);
    throw new HTTPException(500, {
      message: error instanceof Error ? error.message : 'Erro ao processar pagamento'
    });
  }
}
