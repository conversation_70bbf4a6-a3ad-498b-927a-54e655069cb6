import { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import { db } from "@repo/database";
import { checkoutFormSchema, type CheckoutFormData } from "../schema";
import { createCheckoutLink } from "@repo/payments/provider/celcoin";
import { OrderStatus, OrderType } from "@prisma/client";
import crypto from 'crypto';

// Função para gerar uma senha temporária
function generateTemporaryPassword() {
  return crypto.randomBytes(4).toString('hex');
}

export async function processPaymentWithCelcoin(c: Context, input: CheckoutFormData) {
  try {
    // 1. Validar produto
    const product = await db.product.findUnique({
      where: {
        id: input.productId,
        status: 'PUBLISHED'
      },
      include: {
        offers: {
          where: {
            isActive: true,
            OR: [
              {
                AND: [
                  { id: { in: input.orderBumpIds || [] } },
                  { type: 'ORDER_BUMP' },
                ],
              },
              ...(input.offerId ? [{ id: input.offerId }] : []),
            ],
          },
        },
      },
    });

    if (!product) {
      console.error("[PAYMENT_ERROR] Product not found:", input.productId);
      throw new HTTPException(404, { message: 'Produto não encontrado' });
    }

    // 2. Calcular valor total
    let totalAmount = Number(product.priceCents) / 100;

    // If a specific offer is selected, use its price instead of the product price
    if (input.offerId) {
      const selectedOffer = product.offers.find(offer => offer.id === input.offerId);
      if (selectedOffer && selectedOffer.type !== 'ORDER_BUMP') {
        totalAmount = Number(selectedOffer.valueCents) / 100;
      }
    }

    // Add order bumps to the total
    if (input?.orderBumpIds && (input.orderBumpIds.length || 0) > 0) {
      const orderBumpsTotal = product.offers
        .filter(offer => offer.type === 'ORDER_BUMP' && input.orderBumpIds!.includes(offer.id))
        .reduce((acc: any, offer) => acc + (Number(offer.valueCents) / 100), 0);
      totalAmount += orderBumpsTotal;
    }

    // 3. Aplicar cupom de desconto se fornecido
    let discountAmount = 0;
    let appliedCoupon = null;

    if (input.couponCode) {
      const coupon = await db.coupon.findFirst({
        where: {
          code: input.couponCode,
          organizationId: product.organizationId,
          isActive: true,
          OR: [
            { startsAt: null },
            { startsAt: { lte: new Date() } },
          ],
          AND: [
            {
              OR: [
                { expiresAt: null },
                { expiresAt: { gte: new Date() } },
              ],
            },
          ],
        },
      });

      if (coupon) {
        // Verificar se ainda pode ser usado
        if (coupon.maxUses && coupon.usedCount >= coupon.maxUses) {
          throw new HTTPException(400, { message: "Cupom esgotado" });
        }

        // Verificar valor mínimo
        if (coupon.minAmountCents && totalAmount * 100 < coupon.minAmountCents) {
          throw new HTTPException(400, {
            message: `Valor mínimo para este cupom é R$ ${(coupon.minAmountCents / 100).toFixed(2)}`
          });
        }

        // Calcular desconto
        if (coupon.type === "PERCENTAGE") {
          discountAmount = (totalAmount * coupon.valueCents) / 10000;
        } else {
          discountAmount = coupon.valueCents / 100;
        }

        // Garantir que o desconto não seja maior que o valor total
        discountAmount = Math.min(discountAmount, totalAmount);
        appliedCoupon = coupon;

        // Incrementar contador de uso do cupom
        await db.coupon.update({
          where: { id: coupon.id },
          data: { usedCount: { increment: 1 } },
        });
      } else {
        throw new HTTPException(400, { message: "Cupom inválido ou expirado" });
      }
    }

    const finalAmount = totalAmount - discountAmount;

    console.log("[PAYMENT_DEBUG] Price calculation:", {
      basePrice: Number(product.priceCents) / 100,
      offersTotal: (input?.orderBumpIds?.length || 0) > 0 ? totalAmount - (Number(product.priceCents) / 100) + discountAmount : 0,
      discountAmount,
      finalTotal: finalAmount
    });

    // 3. Verificar se o usuário existe ou criar uma nova conta
    let userAccount = null;
    let isNewUser = false;
    let tempPassword = null;

    // Verificar se existe um usuário com o email fornecido
    userAccount = await db.user.findUnique({
      where: { email: input.customerData.email }
    });

    // Se ainda não existir, criar uma nova conta
    if (!userAccount) {
      isNewUser = true;
      tempPassword = generateTemporaryPassword();

      // Criar o usuário no banco de dados
      userAccount = await db.user.create({
        data: {
          email: input.customerData.email,
          name: input.customerData.name,
          emailVerified: false,
        }
      });

      console.log('[PAYMENT_DEBUG] Created new user account:', userAccount.id);
    }

    // 4. Criar ordem no banco
    console.log("[PAYMENT_DEBUG] Creating order");

    // Primeiro, verificar se o usuário tem uma organização ou criar uma
    let organizationId = null;
    const userOrganizations = await db.member.findFirst({
      where: { userId: userAccount.id },
      include: { organization: true }
    });

    if (userOrganizations) {
      organizationId = userOrganizations.organizationId;
    } else {
      // Criar uma organização padrão para o usuário
      const organization = await db.organization.create({
        data: {
          name: `${userAccount.name}'s Organization`,
          slug: `${userAccount.id}-org`,
          members: {
            create: {
              userId: userAccount.id,
              role: 'OWNER',
              createdAt: new Date()
            }
          }
        }
      });
      organizationId = organization.id;
    }

    const orderData = {
      organizationId,
      buyerId: userAccount.id,
      productId: product.id,
      totalCents: Math.round(finalAmount * 100), // Converter para centavos
      status: OrderStatus.PENDING,
      type: OrderType.PURCHASE,
      paymentMethod: input.paymentMethod,
    };

    console.log("[PAYMENT_DEBUG] Order data:", {
      ...orderData,
      paymentData: "HIDDEN"
    });

    const order = await db.order.create({
      data: orderData
    });

    // Create order items for selected order bumps only
    if (input?.orderBumpIds && (input.orderBumpIds.length || 0) > 0) {
      // Filter offers to only include selected order bumps
      const selectedOffers = product.offers.filter(
        (offer) => offer.type === 'ORDER_BUMP' && input.orderBumpIds!.includes(offer.id)
      );

      if (selectedOffers.length > 0) {
        const orderItems = selectedOffers.map((offer) => ({
          orderId: order.id,
          productId: offer.targetProductId || product.id,
          quantity: 1,
          priceCents: Number(offer.valueCents),
          totalCents: Number(offer.valueCents),
        }));

        await db.orderItem.createMany({
          data: orderItems,
        });

        console.log("[PAYMENT_DEBUG] Created order items for selected order bumps:", orderItems.length);
      }
    }

    // 5. Processar pagamento diretamente no Celcoin
    console.log("[PAYMENT_DEBUG] Processing payment directly with Celcoin");
    console.log("[PAYMENT_DEBUG] Payment data:", {
      type: "one-time",
      productId: product.id,
      customerId: userAccount.id,
      organizationId: organizationId,
      userId: userAccount.id,
      email: input.customerData.email,
      name: input.customerData.name,
      value: finalAmount,
      hasCreditCard: !!input.creditCard
    });

    // Importar a função de processamento direto
    const { processPaymentWithCard } = await import("@repo/payments/provider/celcoin/process-payment");

    const paymentResult = await processPaymentWithCard({
      type: "one-time",
      productId: product.id,
      redirectUrl: `${process.env.NEXT_PUBLIC_APP_URL}/checkout/success?orderId=${order.id}`,
      customerId: userAccount.id,
      organizationId: organizationId,
      userId: userAccount.id,
      email: input.customerData.email,
      name: input.customerData.name,
      paymentMethod: input.paymentMethod,
      value: finalAmount,
      creditCardData: input.creditCard ? {
        cardNumber: input.creditCard.cardNumber,
        cardHolder: input.creditCard.cardHolder,
        cardExpiry: input.creditCard.cardExpiry,
        cardCvv: input.creditCard.cardCvv,
        installments: input.creditCard.installments
      } : undefined
    });

    console.log("[PAYMENT_DEBUG] Celcoin payment result:", {
      success: paymentResult.success,
      status: paymentResult.status,
      chargeId: paymentResult.chargeId,
      transactionId: paymentResult.transactionId,
      redirectUrl: paymentResult.redirectUrl,
      paymentLink: paymentResult.paymentLink,
      // PIX specific info
      pixQrCode: paymentResult.pixQrCode,
      pixReference: paymentResult.pixReference,
      pixImage: paymentResult.pixImage,
      pixPage: paymentResult.pixPage,
      // Boleto specific info
      boletoPdf: paymentResult.boletoPdf,
      boletoBankLine: paymentResult.boletoBankLine,
      boletoBankAgency: paymentResult.boletoBankAgency,
      boletoBankAccount: paymentResult.boletoBankAccount
    });

    if (!paymentResult.success) {
      throw new HTTPException(500, { message: 'Falha ao processar pagamento no Celcoin' });
    }

    // Para PIX/Boleto, não redirecionar, retornar informações para mostrar na página
    if (input.paymentMethod === 'PIX' || input.paymentMethod === 'BOLETO') {
      console.log("[PAYMENT_DEBUG] PIX/Boleto payment - returning payment info instead of redirecting");
      
      return c.json({
        orderId: order.id,
        success: true,
        status: paymentResult.status,
        paymentMethod: input.paymentMethod,
        // Informações do PIX
        ...(input.paymentMethod === 'PIX' && {
          pixQrCode: paymentResult.pixQrCode,
          pixReference: paymentResult.pixReference,
          pixImage: paymentResult.pixImage,
          pixPage: paymentResult.pixPage,
          pixEndToEndId: paymentResult.pixEndToEndId,
          pixInstructions: paymentResult.pixInstructions
        }),
        // Informações do Boleto
        ...(input.paymentMethod === 'BOLETO' && {
          boletoPdf: paymentResult.boletoPdf,
          boletoBankLine: paymentResult.boletoBankLine,
          boletoBankAgency: paymentResult.boletoBankAgency,
          boletoBankAccount: paymentResult.boletoBankAccount,
          boletoInstructions: paymentResult.boletoInstructions
        }),
        // Dados completos para debug
        chargeDetails: paymentResult.chargeDetails,
        isNewUser,
        loginLink: isNewUser ? `${process.env.NEXT_PUBLIC_APP_URL}/login?email=${encodeURIComponent(userAccount.email)}` : undefined
      });
    }

    const checkoutUrl = paymentResult.redirectUrl;

    // 6. Atualizar ordem com ID do gateway
    console.log("[PAYMENT_DEBUG] Updating order with payment info");
    const updatedOrder = await db.order.update({
      where: { id: order.id },
      data: {
        paymentId: `celcoin-${Date.now()}`, // ID temporário para Celcoin
      },
    });

    console.log("[PAYMENT_SUCCESS] Order processed:", {
      orderId: updatedOrder.id,
      status: updatedOrder.status
    });

    return c.json({
      orderId: order.id,
      checkoutUrl: checkoutUrl,
      paymentMethod: input.paymentMethod,
      isNewUser,
      loginLink: isNewUser ? `${process.env.NEXT_PUBLIC_APP_URL}/login?email=${encodeURIComponent(userAccount.email)}` : undefined
    });

  } catch (error) {
    console.error("[PAYMENT_ERROR] Full error:", error);

    throw new HTTPException(500, {
      message: error instanceof Error ? error.message : 'Erro ao processar pagamento'
    });
  }
}
