import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { HTTPException } from "hono/http-exception";
import { authMiddleware } from "../../middleware/auth";
import { db } from "@repo/database";

const createPaymentProviderSchema = z.object({
  name: z.string().min(1),
  type: z.enum(["stripe", "celcoin", "asaas", "polar", "lemonsqueezy"]),
  settings: z.record(z.any()),
  isActive: z.boolean().default(true),
});

const updatePaymentProviderSchema = createPaymentProviderSchema.partial();

export const paymentProvidersRouter = new Hono()
  .basePath("/payment-providers")
  .use("*", authMiddleware)

  // GET /api/payment-providers - Listar providers
  .get(
    "/",
    describeRoute({
      tags: ["Payment Providers"],
      summary: "List payment providers",
      description: "Get all payment providers for the organization",
      responses: {
        200: {
          description: "List of payment providers",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      
      // Buscar organização do usuário
      const member = await db.member.findFirst({
        where: {
          userId: user.id,
        },
        include: {
          organization: true,
        },
      });

      if (!member) {
        throw new HTTPException(404, { message: "Organização não encontrada" });
      }

      const providers = await db.paymentProvider.findMany({
        where: {
          organizationId: member.organizationId,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return c.json({ providers });
    },
  )

  // POST /api/payment-providers - Criar provider
  .post(
    "/",
    validator("json", createPaymentProviderSchema),
    describeRoute({
      tags: ["Payment Providers"],
      summary: "Create payment provider",
      description: "Create a new payment provider configuration",
      responses: {
        201: {
          description: "Payment provider created successfully",
        },
        400: {
          description: "Invalid input data",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const data = c.req.valid("json");

      // Buscar organização do usuário
      const member = await db.member.findFirst({
        where: {
          userId: user.id,
        },
        include: {
          organization: true,
        },
      });

      if (!member) {
        throw new HTTPException(404, { message: "Organização não encontrada" });
      }

      // Verificar se já existe um provider do mesmo tipo ativo
      if (data.isActive) {
        const existingProvider = await db.paymentProvider.findFirst({
          where: {
            organizationId: member.organizationId,
            type: data.type,
            isActive: true,
          },
        });

        if (existingProvider) {
          throw new HTTPException(400, { 
            message: `Já existe um provider ${data.type} ativo. Desative o atual primeiro.` 
          });
        }
      }

      const provider = await db.paymentProvider.create({
        data: {
          ...data,
          organizationId: member.organizationId,
        },
      });

      return c.json({ provider }, 201);
    },
  )

  // PUT /api/payment-providers/:id - Atualizar provider
  .put(
    "/:id",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    validator("json", updatePaymentProviderSchema),
    describeRoute({
      tags: ["Payment Providers"],
      summary: "Update payment provider",
      description: "Update a payment provider configuration",
      responses: {
        200: {
          description: "Payment provider updated successfully",
        },
        404: {
          description: "Payment provider not found",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.valid("param");
      const data = c.req.valid("json");
      const user = c.get("user");

      // Buscar organização do usuário
      const member = await db.member.findFirst({
        where: {
          userId: user.id,
        },
        include: {
          organization: true,
        },
      });

      if (!member) {
        throw new HTTPException(404, { message: "Organização não encontrada" });
      }

      // Verificar se o provider existe e pertence à organização
      const existingProvider = await db.paymentProvider.findFirst({
        where: {
          id,
          organizationId: member.organizationId,
        },
      });

      if (!existingProvider) {
        throw new HTTPException(404, { message: "Provider não encontrado" });
      }

      // Se está ativando um provider, desativar outros do mesmo tipo
      if (data.isActive && data.type) {
        await db.paymentProvider.updateMany({
          where: {
            organizationId: member.organizationId,
            type: data.type,
            isActive: true,
            id: { not: id },
          },
          data: {
            isActive: false,
          },
        });
      }

      const provider = await db.paymentProvider.update({
        where: { id },
        data,
      });

      return c.json({ provider });
    },
  )

  // DELETE /api/payment-providers/:id - Deletar provider
  .delete(
    "/:id",
    validator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Payment Providers"],
      summary: "Delete payment provider",
      description: "Delete a payment provider configuration",
      responses: {
        200: {
          description: "Payment provider deleted successfully",
        },
        404: {
          description: "Payment provider not found",
        },
      },
    }),
    async (c) => {
      const { id } = c.req.valid("param");
      const user = c.get("user");

      // Buscar organização do usuário
      const member = await db.member.findFirst({
        where: {
          userId: user.id,
        },
        include: {
          organization: true,
        },
      });

      if (!member) {
        throw new HTTPException(404, { message: "Organização não encontrada" });
      }

      // Verificar se o provider existe e pertence à organização
      const existingProvider = await db.paymentProvider.findFirst({
        where: {
          id,
          organizationId: member.organizationId,
        },
      });

      if (!existingProvider) {
        throw new HTTPException(404, { message: "Provider não encontrado" });
      }

      await db.paymentProvider.delete({
        where: { id },
      });

      return c.json({ message: "Provider deletado com sucesso" });
    },
  );
