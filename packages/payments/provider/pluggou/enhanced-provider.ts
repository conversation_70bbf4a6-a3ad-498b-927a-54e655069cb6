import { BasePaymentProvider } from "../../src/lib/base-provider";
import {
  PaymentRequest,
  PaymentResponse,
  RefundRequest,
  RefundResponse,
  WebhookEvent,
  PaymentMethod,
} from "../../src/types/payment-provider";

/**
 * Configuração específica do Pluggou
 */
interface PluggouConfig {
  apiKey: string;
  organizationId: string;
  baseUrl: string;
  environment: 'sandbox' | 'production';
}

/**
 * Resposta da API Pluggou para criação de transação
 */
interface PluggouTransactionResponse {
  id: string;
  status: 'pending' | 'paid' | 'cancelled' | 'expired';
  amount: number;
  currency: string;
  pixCode: string;
  pixQrCode: string;
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Provider Pluggou implementando a interface base
 */
export class EnhancedPluggouProvider extends BasePaymentProvider {
  readonly id = 'pluggou';
  readonly name = 'Pluggou';
  readonly type = 'pluggou';
  readonly version = '2.0.0';

  private pluggouConfig: PluggouConfig | null = null;

  async initialize(): Promise<void> {
    await super.initialize();
    
    if (!this.config) {
      throw new Error('Provider not configured');
    }

    this.pluggouConfig = {
      apiKey: this.config.settings.apiKey,
      organizationId: this.config.settings.organizationId,
      baseUrl: this.config.settings.baseUrl || 'https://app.pluggou.io/api',
      environment: this.config.settings.environment || 'production',
    };

    // Validar configuração
    if (!this.pluggouConfig.apiKey || !this.pluggouConfig.organizationId) {
      throw new Error('Missing required Pluggou configuration: apiKey and organizationId');
    }
  }

  protected async doCreatePayment(request: PaymentRequest): Promise<PaymentResponse> {
    if (!this.pluggouConfig) {
      throw new Error('Provider not initialized');
    }

    this.log('info', 'Creating PIX payment', { 
      amount: request.amount, 
      customer: request.customer.email 
    });

    const response = await this.makeApiRequest('/transactions', {
      method: 'POST',
      body: JSON.stringify({
        amount: request.amount,
        currency: request.currency,
        customer: {
          name: request.customer.name,
          email: request.customer.email,
          document: request.customer.document,
          phone: request.customer.phone,
        },
        paymentMethod: 'pix',
        description: request.description,
        orderId: request.orderId,
        metadata: request.metadata,
      }),
    });

    const pluggouResponse: PluggouTransactionResponse = response;

    return {
      id: pluggouResponse.id,
      status: this.mapStatus(pluggouResponse.status),
      amount: pluggouResponse.amount,
      currency: pluggouResponse.currency,
      paymentMethod: 'pix',
      transactionId: pluggouResponse.id,
      externalId: pluggouResponse.id,
      pixCode: pluggouResponse.pixCode,
      pixQrCode: pluggouResponse.pixQrCode,
      createdAt: new Date(pluggouResponse.createdAt),
      updatedAt: new Date(pluggouResponse.updatedAt),
    };
  }

  protected async doGetPayment(paymentId: string): Promise<PaymentResponse> {
    if (!this.pluggouConfig) {
      throw new Error('Provider not initialized');
    }

    this.log('info', 'Getting payment status', { paymentId });

    const response = await this.makeApiRequest(`/transactions/${paymentId}`, {
      method: 'GET',
    });

    const pluggouResponse: PluggouTransactionResponse = response;

    return {
      id: pluggouResponse.id,
      status: this.mapStatus(pluggouResponse.status),
      amount: pluggouResponse.amount,
      currency: pluggouResponse.currency,
      paymentMethod: 'pix',
      transactionId: pluggouResponse.id,
      externalId: pluggouResponse.id,
      pixCode: pluggouResponse.pixCode,
      pixQrCode: pluggouResponse.pixQrCode,
      createdAt: new Date(pluggouResponse.createdAt),
      updatedAt: new Date(pluggouResponse.updatedAt),
    };
  }

  protected async doCancelPayment(paymentId: string): Promise<PaymentResponse> {
    if (!this.pluggouConfig) {
      throw new Error('Provider not initialized');
    }

    this.log('info', 'Cancelling payment', { paymentId });

    const response = await this.makeApiRequest(`/transactions/${paymentId}/cancel`, {
      method: 'POST',
    });

    const pluggouResponse: PluggouTransactionResponse = response;

    return {
      id: pluggouResponse.id,
      status: this.mapStatus(pluggouResponse.status),
      amount: pluggouResponse.amount,
      currency: pluggouResponse.currency,
      paymentMethod: 'pix',
      transactionId: pluggouResponse.id,
      externalId: pluggouResponse.id,
      createdAt: new Date(pluggouResponse.createdAt),
      updatedAt: new Date(pluggouResponse.updatedAt),
    };
  }

  protected async doCreateRefund(request: RefundRequest): Promise<RefundResponse> {
    // Pluggou não suporta reembolsos automáticos via API
    throw new Error('Pluggou does not support automatic refunds via API');
  }

  protected async doGetRefund(refundId: string): Promise<RefundResponse> {
    throw new Error('Pluggou does not support refund queries via API');
  }

  protected async doHandleWebhook(req: Request): Promise<WebhookEvent> {
    const payload = await req.text();
    const signature = req.headers.get('x-signature') || '';

    // Validar webhook
    const isValid = await this.doValidateWebhook(req);
    if (!isValid) {
      throw new Error('Invalid webhook signature');
    }

    const webhookData = JSON.parse(payload);
    
    this.log('info', 'Processing webhook', { 
      type: webhookData.type, 
      transactionId: webhookData.data?.id 
    });

    return {
      id: webhookData.data?.id || 'unknown',
      type: webhookData.type,
      data: webhookData.data,
      timestamp: new Date(),
      processed: true,
    };
  }

  protected async doValidateWebhook(req: Request): Promise<boolean> {
    // Pluggou não usa assinatura de webhook por enquanto
    // TODO: Implementar validação quando disponível na API
    return true;
  }

  protected async doHealthCheck(): Promise<boolean> {
    if (!this.pluggouConfig) {
      return false;
    }

    try {
      // Fazer uma requisição simples para verificar conectividade
      await this.makeApiRequest('/health', { method: 'GET' });
      return true;
    } catch (error) {
      this.log('error', 'Health check failed', error);
      return false;
    }
  }

  getSupportedMethods(): PaymentMethod[] {
    return [
      {
        id: 'pix',
        name: 'PIX',
        type: 'pix',
        enabled: true,
      },
    ];
  }

  /**
   * Fazer requisição para API do Pluggou
   */
  private async makeApiRequest(endpoint: string, options: RequestInit): Promise<any> {
    if (!this.pluggouConfig) {
      throw new Error('Provider not configured');
    }

    const url = `${this.pluggouConfig.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.pluggouConfig.apiKey}`,
        'X-Organization-ID': this.pluggouConfig.organizationId,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      this.log('error', 'API request failed', {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });
      throw new Error(`Pluggou API error: ${response.status} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * Mapear status do Pluggou para status padrão
   */
  private mapStatus(pluggouStatus: string): PaymentResponse['status'] {
    const statusMap: Record<string, PaymentResponse['status']> = {
      'pending': 'pending',
      'paid': 'approved',
      'cancelled': 'cancelled',
      'expired': 'cancelled',
    };

    return statusMap[pluggouStatus] || 'pending';
  }
}
