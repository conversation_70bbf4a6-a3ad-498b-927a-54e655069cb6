{"dependencies": {"@lemonsqueezy/lemonsqueezy.js": "^4.0.0", "@polar-sh/sdk": "^0.33.0", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/logs": "workspace:*", "chargebee-typescript": "^2.48.0", "stripe": "^18.2.1", "ufo": "^1.6.1"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.15.30", "vitest": "^1.2.0", "@vitest/coverage-v8": "^1.2.0"}, "main": "./index.ts", "exports": {".": {"types": "./index.ts", "default": "./index.ts"}, "./types": "./types.ts", "./lib/helper": "./src/lib/helper.ts", "./provider/pluggou": "./provider/pluggou/index.ts", "./provider/celcoin": "./provider/celcoin/index.ts", "./provider/celcoin/process-payment": "./provider/celcoin/process-payment.ts", "./provider/stripe": "./provider/stripe/index.ts", "./provider/asaas": "./provider/asaas/index.ts", "./src/lib/provider-manager": "./src/lib/provider-manager.ts", "./src/lib/payment-factory": "./src/lib/payment-factory.ts", "./src/lib/enhanced-payment-factory": "./src/lib/enhanced-payment-factory.ts", "./src/lib/base-provider": "./src/lib/base-provider.ts", "./src/lib/monitoring-system": "./src/lib/monitoring-system.ts", "./src/types/payment-provider": "./src/types/payment-provider.ts"}, "name": "@repo/payments", "scripts": {"type-check": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "types": "./**/.ts", "version": "2.0.0"}