import { 
  IPaymentProvider, 
  AdvancedProviderConfig, 
  PaymentRequest, 
  PaymentResponse, 
  RefundRequest, 
  RefundResponse, 
  WebhookEvent, 
  PaymentMethod,
  ProviderHealthStatus,
  ProviderMetrics
} from '../types/payment-provider';

/**
 * Classe base abstrata para todos os providers de pagamento
 * Implementa funcionalidades comuns e define interface padrão
 */
export abstract class BasePaymentProvider implements IPaymentProvider {
  protected config: AdvancedProviderConfig | null = null;
  protected initialized = false;
  protected metrics: ProviderMetrics = {
    totalTransactions: 0,
    successfulTransactions: 0,
    failedTransactions: 0,
    averageResponseTime: 0,
    uptime: 100,
  };

  abstract readonly id: string;
  abstract readonly name: string;
  abstract readonly type: string;
  abstract readonly version: string;

  // Métodos abstratos que devem ser implementados pelos providers
  protected abstract doCreatePayment(request: PaymentRequest): Promise<PaymentResponse>;
  protected abstract doGetPayment(paymentId: string): Promise<PaymentResponse>;
  protected abstract doCancelPayment(paymentId: string): Promise<PaymentResponse>;
  protected abstract doCreateRefund(request: RefundRequest): Promise<RefundResponse>;
  protected abstract doGetRefund(refundId: string): Promise<RefundResponse>;
  protected abstract doHandleWebhook(req: Request): Promise<WebhookEvent>;
  protected abstract doValidateWebhook(req: Request): Promise<boolean>;
  protected abstract doHealthCheck(): Promise<boolean>;

  async configure(config: AdvancedProviderConfig): Promise<void> {
    this.config = config;
    await this.initialize();
  }

  isConfigured(): boolean {
    return this.config !== null && this.initialized;
  }

  getConfiguration(): AdvancedProviderConfig | null {
    return this.config;
  }

  async initialize(): Promise<void> {
    if (!this.config) {
      throw new Error(`Provider ${this.id} not configured`);
    }
    this.initialized = true;
  }

  async destroy(): Promise<void> {
    this.initialized = false;
    this.config = null;
  }

  // Métodos com instrumentação e retry
  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    return this.executeWithRetry(
      () => this.doCreatePayment(request),
      'createPayment'
    );
  }

  async getPayment(paymentId: string): Promise<PaymentResponse> {
    return this.executeWithRetry(
      () => this.doGetPayment(paymentId),
      'getPayment'
    );
  }

  async cancelPayment(paymentId: string): Promise<PaymentResponse> {
    return this.executeWithRetry(
      () => this.doCancelPayment(paymentId),
      'cancelPayment'
    );
  }

  async createRefund(request: RefundRequest): Promise<RefundResponse> {
    return this.executeWithRetry(
      () => this.doCreateRefund(request),
      'createRefund'
    );
  }

  async getRefund(refundId: string): Promise<RefundResponse> {
    return this.executeWithRetry(
      () => this.doGetRefund(refundId),
      'getRefund'
    );
  }

  async handleWebhook(req: Request): Promise<WebhookEvent> {
    return this.doHandleWebhook(req);
  }

  async validateWebhook(req: Request): Promise<boolean> {
    return this.doValidateWebhook(req);
  }

  async healthCheck(): Promise<ProviderHealthStatus> {
    const startTime = Date.now();
    let isHealthy = false;
    let error: string | undefined;

    try {
      isHealthy = await this.doHealthCheck();
    } catch (e) {
      error = e instanceof Error ? e.message : 'Unknown error';
    }

    const responseTime = Date.now() - startTime;

    return {
      isHealthy,
      lastCheck: new Date(),
      responseTime,
      errorCount: isHealthy ? 0 : this.metrics.failedTransactions,
      lastError: error,
    };
  }

  async getMetrics(): Promise<ProviderMetrics> {
    return { ...this.metrics };
  }

  abstract getSupportedMethods(): PaymentMethod[];

  isMethodSupported(methodType: string): boolean {
    return this.getSupportedMethods().some(method => method.type === methodType);
  }

  // Utilitários protegidos
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    const maxRetries = this.config?.maxRetries || 3;
    const timeout = this.config?.timeout || 30000;

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const startTime = Date.now();
        
        // Aplicar timeout
        const result = await Promise.race([
          operation(),
          new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('Operation timeout')), timeout)
          )
        ]);

        // Atualizar métricas de sucesso
        const responseTime = Date.now() - startTime;
        this.updateMetrics(true, responseTime);

        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        // Atualizar métricas de erro
        this.updateMetrics(false, 0);

        console.warn(
          `Provider ${this.id} ${operationName} attempt ${attempt}/${maxRetries} failed:`,
          lastError.message
        );

        // Se não é a última tentativa, aguardar antes de tentar novamente
        if (attempt < maxRetries) {
          await this.delay(Math.pow(2, attempt) * 1000); // Exponential backoff
        }
      }
    }

    throw lastError || new Error(`Operation ${operationName} failed after ${maxRetries} attempts`);
  }

  protected updateMetrics(success: boolean, responseTime: number): void {
    this.metrics.totalTransactions++;
    
    if (success) {
      this.metrics.successfulTransactions++;
    } else {
      this.metrics.failedTransactions++;
    }

    // Atualizar tempo médio de resposta
    if (responseTime > 0) {
      const totalResponseTime = this.metrics.averageResponseTime * (this.metrics.totalTransactions - 1);
      this.metrics.averageResponseTime = (totalResponseTime + responseTime) / this.metrics.totalTransactions;
    }

    this.metrics.lastTransactionAt = new Date();
  }

  protected delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const logMessage = `[${this.id.toUpperCase()}] ${message}`;
    
    switch (level) {
      case 'info':
        console.log(logMessage, data || '');
        break;
      case 'warn':
        console.warn(logMessage, data || '');
        break;
      case 'error':
        console.error(logMessage, data || '');
        break;
    }
  }
}
