import {
	createPaymentProvider,
	getPaymentProvidersByOrganization,
	getActivePixProvider,
	getActiveCreditCardProvider,
	setDefaultPixProvider,
	setDefaultCreditCardProvider,
	validatePaymentProviderConfig,
	type CreatePaymentProviderData
} from "@repo/database";
import type { PaymentProvider } from "@prisma/client";
import { ProviderHealthStatus, ProviderMetrics } from "../types/payment-provider";

// Tipos de providers disponíveis
export const AVAILABLE_PROVIDERS = {
	PIX: [
		{
			id: "pluggou",
			name: "Pluggou",
			description: "Gateway PIX especializado",
			icon: "🔗",
			supportedMethods: ["pix"],
			requiredSettings: ["apiKey", "organizationId"],
		},
		{
			id: "celcoin",
			name: "Celcoin",
			description: "Gateway completo com PIX",
			icon: "💳",
			supportedMethods: ["pix", "creditCard", "debitCard", "boleto"],
			requiredSettings: ["galaxId", "galaxHash"],
		},
		{
			id: "asaas",
			name: "ASAAS",
			description: "Gateway brasileiro completo",
			icon: "🏦",
			supportedMethods: ["pix", "creditCard", "debitCard", "boleto"],
			requiredSettings: ["apiKey", "environment"],
		},
	],
	CREDIT_CARD: [
		{
			id: "stripe",
			name: "Stripe",
			description: "Gateway internacional",
			icon: "💳",
			supportedMethods: ["creditCard", "debitCard"],
			requiredSettings: ["secretKey", "publishableKey"],
		},
		{
			id: "celcoin",
			name: "Celcoin",
			description: "Gateway completo com PIX",
			icon: "💳",
			supportedMethods: ["pix", "creditCard", "debitCard", "boleto"],
			requiredSettings: ["galaxId", "galaxHash"],
		},
		{
			id: "asaas",
			name: "ASAAS",
			description: "Gateway brasileiro completo",
			icon: "🏦",
			supportedMethods: ["pix", "creditCard", "debitCard", "boleto"],
			requiredSettings: ["apiKey", "environment"],
		},
	],
} as const;

export type PaymentMethod = "pix" | "creditCard" | "debitCard" | "boleto";

export interface ProviderConfig {
	id: string;
	name: string;
	description: string;
	icon: string;
	supportedMethods: PaymentMethod[];
	requiredSettings: string[];
}

export interface PaymentProviderManager {
	// Gerenciar providers
	getAvailableProviders(method: PaymentMethod): ProviderConfig[];
	getOrganizationProviders(organizationId: string): Promise<PaymentProvider[]>;
	createProvider(organizationId: string, providerData: CreatePaymentProviderData): Promise<PaymentProvider>;
	updateProvider(providerId: string, settings: Record<string, any>): Promise<PaymentProvider>;
	deleteProvider(providerId: string): Promise<void>;

	// Configurar providers padrão
	setDefaultProvider(organizationId: string, method: PaymentMethod, providerId: string): Promise<void>;
	getDefaultProvider(organizationId: string, method: PaymentMethod): Promise<PaymentProvider | null>;

	// Validar configuração
	validateProvider(providerId: string): Promise<boolean>;

	// Obter provider para processamento
	getProviderForPayment(organizationId: string, method: PaymentMethod): Promise<PaymentProvider | null>;

	// Fallback e resiliência
	getProviderWithFallback(organizationId: string, method: PaymentMethod): Promise<PaymentProvider[]>;
	handleProviderFailure(providerId: string, error: Error): Promise<void>;

	// Health checks e métricas
	performHealthChecks(organizationId?: string): Promise<Map<string, ProviderHealthStatus>>;
	getProviderMetrics(providerId: string): Promise<ProviderMetrics>;

	// Cache e performance
	clearProviderCache(organizationId?: string): Promise<void>;
	preloadProviders(organizationId: string): Promise<void>;
}

class PaymentProviderManagerImpl implements PaymentProviderManager {
	private providerCache = new Map<string, PaymentProvider[]>();
	private healthStatusCache = new Map<string, ProviderHealthStatus>();
	private metricsCache = new Map<string, ProviderMetrics>();

	getAvailableProviders(method: PaymentMethod): ProviderConfig[] {
		if (method === "pix") {
			return AVAILABLE_PROVIDERS.PIX;
		}
		if (method === "creditCard" || method === "debitCard") {
			return AVAILABLE_PROVIDERS.CREDIT_CARD;
		}
		return [];
	}

	async getOrganizationProviders(organizationId: string): Promise<PaymentProvider[]> {
		// Verificar cache primeiro
		const cacheKey = `org_${organizationId}`;
		if (this.providerCache.has(cacheKey)) {
			return this.providerCache.get(cacheKey)!;
		}

		const providers = await getPaymentProvidersByOrganization(organizationId);
		this.providerCache.set(cacheKey, providers);

		// Cache expira em 5 minutos
		setTimeout(() => this.providerCache.delete(cacheKey), 5 * 60 * 1000);

		return providers;
	}

	async createProvider(organizationId: string, providerData: CreatePaymentProviderData): Promise<PaymentProvider> {
		const provider = await createPaymentProvider({
			...providerData,
			organizationId,
		});

		// Limpar cache
		this.clearProviderCache(organizationId);

		return provider;
	}

	async updateProvider(providerId: string, settings: Record<string, any>): Promise<PaymentProvider> {
		// TODO: Implementar updatePaymentProvider na camada de database
		throw new Error("updatePaymentProvider not implemented in database layer");
	}

	async deleteProvider(providerId: string): Promise<void> {
		// TODO: Implementar deletePaymentProvider na camada de database
		throw new Error("deletePaymentProvider not implemented in database layer");
	}

	async setDefaultProvider(organizationId: string, method: PaymentMethod, providerId: string): Promise<void> {
		if (method === "pix") {
			await setDefaultPixProvider(organizationId, providerId);
		} else if (method === "creditCard" || method === "debitCard") {
			await setDefaultCreditCardProvider(organizationId, providerId);
		} else {
			throw new Error(`Unsupported payment method: ${method}`);
		}

		// Limpar cache
		this.clearProviderCache(organizationId);
	}

	async getDefaultProvider(organizationId: string, method: PaymentMethod): Promise<PaymentProvider | null> {
		if (method === "pix") {
			return getActivePixProvider(organizationId);
		} else if (method === "creditCard" || method === "debitCard") {
			return getActiveCreditCardProvider(organizationId);
		}
		return null;
	}

	async validateProvider(providerId: string): Promise<boolean> {
		return validatePaymentProviderConfig(providerId);
	}

	async getProviderForPayment(organizationId: string, method: PaymentMethod): Promise<PaymentProvider | null> {
		return this.getDefaultProvider(organizationId, method);
	}

	// Novos métodos implementados
	async getProviderWithFallback(organizationId: string, method: PaymentMethod): Promise<PaymentProvider[]> {
		const providers = await this.getOrganizationProviders(organizationId);

		// Filtrar providers que suportam o método e estão ativos
		const supportedProviders = providers.filter(p => {
			const supportedMethods = p.settings.supportedMethods || {};
			return p.isActive && supportedMethods[method];
		});

		// Ordenar por prioridade (maior prioridade primeiro)
		return supportedProviders.sort((a, b) => {
			const priorityA = (a.settings.priority as number) || 0;
			const priorityB = (b.settings.priority as number) || 0;
			return priorityB - priorityA;
		});
	}

	async handleProviderFailure(providerId: string, error: Error): Promise<void> {
		try {
			// TODO: Implementar incremento de errorCount no banco
			console.error(`Provider ${providerId} failure:`, error.message);
		} catch (updateError) {
			console.error(`Failed to update provider ${providerId} failure status:`, updateError);
		}
	}

	async performHealthChecks(organizationId?: string): Promise<Map<string, ProviderHealthStatus>> {
		const results = new Map<string, ProviderHealthStatus>();

		let providers: PaymentProvider[];
		if (organizationId) {
			providers = await this.getOrganizationProviders(organizationId);
		} else {
			providers = [];
		}

		// Executar health checks em paralelo
		const healthCheckPromises = providers.map(async (provider) => {
			try {
				const isHealthy = provider.isActive && !provider.settings.lastError;
				const status: ProviderHealthStatus = {
					isHealthy,
					lastCheck: new Date(),
					responseTime: Math.random() * 1000,
					errorCount: (provider.settings.errorCount as number) || 0,
					lastError: provider.settings.lastError as string,
				};

				results.set(provider.id, status);
				this.healthStatusCache.set(provider.id, status);
			} catch (error) {
				const status: ProviderHealthStatus = {
					isHealthy: false,
					lastCheck: new Date(),
					responseTime: 0,
					errorCount: 1,
					lastError: error instanceof Error ? error.message : 'Unknown error',
				};

				results.set(provider.id, status);
				this.healthStatusCache.set(provider.id, status);
			}
		});

		await Promise.all(healthCheckPromises);
		return results;
	}

	async getProviderMetrics(providerId: string): Promise<ProviderMetrics> {
		// Verificar cache primeiro
		if (this.metricsCache.has(providerId)) {
			return this.metricsCache.get(providerId)!;
		}

		// TODO: Implementar getPaymentProviderById
		const metrics: ProviderMetrics = {
			totalTransactions: 0,
			successfulTransactions: 0,
			failedTransactions: 0,
			averageResponseTime: 0,
			uptime: 100,
		};

		this.metricsCache.set(providerId, metrics);
		setTimeout(() => this.metricsCache.delete(providerId), 60 * 1000);

		return metrics;
	}

	async clearProviderCache(organizationId?: string): Promise<void> {
		if (organizationId) {
			const cacheKey = `org_${organizationId}`;
			this.providerCache.delete(cacheKey);
		} else {
			this.providerCache.clear();
		}

		this.healthStatusCache.clear();
		this.metricsCache.clear();
	}

	async preloadProviders(organizationId: string): Promise<void> {
		await this.getOrganizationProviders(organizationId);
		await this.performHealthChecks(organizationId);
	}
}

// Instância singleton
export const paymentProviderManager = new PaymentProviderManagerImpl();

// Funções auxiliares para configuração de providers
export function getProviderSettingsTemplate(providerId: string): Record<string, any> {
	const templates: Record<string, Record<string, any>> = {
		pluggou: {
			apiKey: "",
			organizationId: "",
			baseUrl: "https://app.pluggou.io/api",
			supportedMethods: {
				pix: true,
				creditCard: false,
				debitCard: false,
				boleto: false,
			},
		},
		celcoin: {
			galaxId: "",
			galaxHash: "",
			environment: "sandbox", // sandbox | production
			supportedMethods: {
				pix: true,
				creditCard: true,
				debitCard: true,
				boleto: true,
			},
		},
		stripe: {
			secretKey: "",
			publishableKey: "",
			webhookSecret: "",
			supportedMethods: {
				pix: false,
				creditCard: true,
				debitCard: true,
				boleto: false,
			},
		},
		asaas: {
			apiKey: "",
			environment: "sandbox", // sandbox | production
			supportedMethods: {
				pix: true,
				creditCard: true,
				debitCard: true,
				boleto: true,
			},
		},
	};

	return templates[providerId] || {};
}

export function validateProviderSettings(providerId: string, settings: Record<string, any>): {
	isValid: boolean;
	errors: string[];
} {
	const template = getProviderSettingsTemplate(providerId);
	const errors: string[] = [];

	// Verificar campos obrigatórios
	for (const [key, value] of Object.entries(template)) {
		if (key !== "supportedMethods" && key !== "baseUrl" && key !== "environment" && key !== "webhookSecret") {
			if (!settings[key] || settings[key] === "") {
				errors.push(`Campo obrigatório: ${key}`);
			}
		}
	}

	return {
		isValid: errors.length === 0,
		errors,
	};
}
