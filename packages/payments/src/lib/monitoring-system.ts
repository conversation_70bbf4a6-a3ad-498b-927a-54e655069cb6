import { PaymentProvider } from "@prisma/client";
import { ProviderHealthStatus, ProviderMetrics } from "../types/payment-provider";
import { paymentProviderManager } from "./provider-manager";

/**
 * Tipos de alertas
 */
export enum AlertType {
  PROVIDER_DOWN = 'provider_down',
  HIGH_ERROR_RATE = 'high_error_rate',
  SLOW_RESPONSE = 'slow_response',
  LOW_SUCCESS_RATE = 'low_success_rate',
  PROVIDER_RECOVERED = 'provider_recovered',
}

/**
 * Severidade do alerta
 */
export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Interface do alerta
 */
export interface Alert {
  id: string;
  type: AlertType;
  severity: AlertSeverity;
  providerId: string;
  providerName: string;
  message: string;
  data: Record<string, any>;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
}

/**
 * Configuração de thresholds para alertas
 */
export interface AlertThresholds {
  errorRateThreshold: number; // Porcentagem
  responseTimeThreshold: number; // Milissegundos
  successRateThreshold: number; // Porcentagem
  uptimeThreshold: number; // Porcentagem
}

/**
 * Canal de notificação
 */
export interface NotificationChannel {
  type: 'email' | 'webhook' | 'slack' | 'sms';
  config: Record<string, any>;
  enabled: boolean;
}

/**
 * Sistema de monitoramento de providers
 */
export class PaymentProviderMonitoringSystem {
  private alerts: Map<string, Alert> = new Map();
  private thresholds: AlertThresholds = {
    errorRateThreshold: 5, // 5%
    responseTimeThreshold: 5000, // 5 segundos
    successRateThreshold: 95, // 95%
    uptimeThreshold: 99, // 99%
  };
  private notificationChannels: NotificationChannel[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(customThresholds?: Partial<AlertThresholds>) {
    if (customThresholds) {
      this.thresholds = { ...this.thresholds, ...customThresholds };
    }
  }

  /**
   * Iniciar monitoramento automático
   */
  startMonitoring(intervalMinutes: number = 5): void {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    this.monitoringInterval = setInterval(async () => {
      await this.performMonitoringCheck();
    }, intervalMinutes * 60 * 1000);

    console.log(`Payment provider monitoring started (interval: ${intervalMinutes} minutes)`);
  }

  /**
   * Parar monitoramento automático
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('Payment provider monitoring stopped');
    }
  }

  /**
   * Executar verificação de monitoramento
   */
  async performMonitoringCheck(organizationId?: string): Promise<void> {
    try {
      const healthStatuses = await paymentProviderManager.performHealthChecks(organizationId);
      
      for (const [providerId, healthStatus] of healthStatuses) {
        await this.checkProviderHealth(providerId, healthStatus);
        
        // Verificar métricas se o provider estiver saudável
        if (healthStatus.isHealthy) {
          const metrics = await paymentProviderManager.getProviderMetrics(providerId);
          await this.checkProviderMetrics(providerId, metrics);
        }
      }
    } catch (error) {
      console.error('Error during monitoring check:', error);
    }
  }

  /**
   * Verificar saúde do provider
   */
  private async checkProviderHealth(providerId: string, healthStatus: ProviderHealthStatus): Promise<void> {
    const alertId = `${providerId}_health`;
    const existingAlert = this.alerts.get(alertId);

    if (!healthStatus.isHealthy) {
      // Provider com problemas
      if (!existingAlert || existingAlert.resolved) {
        const alert: Alert = {
          id: alertId,
          type: AlertType.PROVIDER_DOWN,
          severity: AlertSeverity.CRITICAL,
          providerId,
          providerName: await this.getProviderName(providerId),
          message: `Provider ${providerId} is down or unhealthy`,
          data: {
            lastError: healthStatus.lastError,
            errorCount: healthStatus.errorCount,
            responseTime: healthStatus.responseTime,
          },
          timestamp: new Date(),
          resolved: false,
        };

        this.alerts.set(alertId, alert);
        await this.sendNotification(alert);
      }
    } else {
      // Provider saudável
      if (existingAlert && !existingAlert.resolved) {
        // Provider se recuperou
        existingAlert.resolved = true;
        existingAlert.resolvedAt = new Date();

        const recoveryAlert: Alert = {
          id: `${alertId}_recovery`,
          type: AlertType.PROVIDER_RECOVERED,
          severity: AlertSeverity.LOW,
          providerId,
          providerName: await this.getProviderName(providerId),
          message: `Provider ${providerId} has recovered`,
          data: {
            downtime: existingAlert.resolvedAt.getTime() - existingAlert.timestamp.getTime(),
          },
          timestamp: new Date(),
          resolved: true,
        };

        await this.sendNotification(recoveryAlert);
      }
    }
  }

  /**
   * Verificar métricas do provider
   */
  private async checkProviderMetrics(providerId: string, metrics: ProviderMetrics): Promise<void> {
    const providerName = await this.getProviderName(providerId);

    // Verificar taxa de erro
    const errorRate = metrics.totalTransactions > 0 
      ? (metrics.failedTransactions / metrics.totalTransactions) * 100 
      : 0;

    if (errorRate > this.thresholds.errorRateThreshold) {
      await this.createAlert({
        id: `${providerId}_error_rate`,
        type: AlertType.HIGH_ERROR_RATE,
        severity: errorRate > 10 ? AlertSeverity.HIGH : AlertSeverity.MEDIUM,
        providerId,
        providerName,
        message: `High error rate detected: ${errorRate.toFixed(2)}%`,
        data: { errorRate, threshold: this.thresholds.errorRateThreshold },
      });
    }

    // Verificar tempo de resposta
    if (metrics.averageResponseTime > this.thresholds.responseTimeThreshold) {
      await this.createAlert({
        id: `${providerId}_slow_response`,
        type: AlertType.SLOW_RESPONSE,
        severity: metrics.averageResponseTime > 10000 ? AlertSeverity.HIGH : AlertSeverity.MEDIUM,
        providerId,
        providerName,
        message: `Slow response time detected: ${metrics.averageResponseTime}ms`,
        data: { responseTime: metrics.averageResponseTime, threshold: this.thresholds.responseTimeThreshold },
      });
    }

    // Verificar taxa de sucesso
    const successRate = metrics.totalTransactions > 0 
      ? (metrics.successfulTransactions / metrics.totalTransactions) * 100 
      : 100;

    if (successRate < this.thresholds.successRateThreshold) {
      await this.createAlert({
        id: `${providerId}_low_success`,
        type: AlertType.LOW_SUCCESS_RATE,
        severity: successRate < 90 ? AlertSeverity.HIGH : AlertSeverity.MEDIUM,
        providerId,
        providerName,
        message: `Low success rate detected: ${successRate.toFixed(2)}%`,
        data: { successRate, threshold: this.thresholds.successRateThreshold },
      });
    }
  }

  /**
   * Criar alerta
   */
  private async createAlert(alertData: Omit<Alert, 'timestamp' | 'resolved'>): Promise<void> {
    const existingAlert = this.alerts.get(alertData.id);
    
    // Evitar spam de alertas - só criar se não existir ou foi resolvido há mais de 1 hora
    if (existingAlert && !existingAlert.resolved) {
      const hoursSinceAlert = (Date.now() - existingAlert.timestamp.getTime()) / (1000 * 60 * 60);
      if (hoursSinceAlert < 1) {
        return;
      }
    }

    const alert: Alert = {
      ...alertData,
      timestamp: new Date(),
      resolved: false,
    };

    this.alerts.set(alert.id, alert);
    await this.sendNotification(alert);
  }

  /**
   * Enviar notificação
   */
  private async sendNotification(alert: Alert): Promise<void> {
    for (const channel of this.notificationChannels) {
      if (!channel.enabled) continue;

      try {
        switch (channel.type) {
          case 'webhook':
            await this.sendWebhookNotification(alert, channel.config);
            break;
          case 'email':
            await this.sendEmailNotification(alert, channel.config);
            break;
          case 'slack':
            await this.sendSlackNotification(alert, channel.config);
            break;
          default:
            console.warn(`Unsupported notification channel: ${channel.type}`);
        }
      } catch (error) {
        console.error(`Failed to send notification via ${channel.type}:`, error);
      }
    }

    // Log do alerta
    console.log(`[ALERT] ${alert.severity.toUpperCase()} - ${alert.message}`, {
      providerId: alert.providerId,
      type: alert.type,
      data: alert.data,
    });
  }

  /**
   * Enviar notificação via webhook
   */
  private async sendWebhookNotification(alert: Alert, config: Record<string, any>): Promise<void> {
    const response = await fetch(config.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(config.headers || {}),
      },
      body: JSON.stringify({
        alert,
        timestamp: alert.timestamp.toISOString(),
      }),
    });

    if (!response.ok) {
      throw new Error(`Webhook notification failed: ${response.statusText}`);
    }
  }

  /**
   * Enviar notificação via email (placeholder)
   */
  private async sendEmailNotification(alert: Alert, config: Record<string, any>): Promise<void> {
    // TODO: Implementar envio de email
    console.log('Email notification would be sent:', { alert, config });
  }

  /**
   * Enviar notificação via Slack (placeholder)
   */
  private async sendSlackNotification(alert: Alert, config: Record<string, any>): Promise<void> {
    // TODO: Implementar notificação Slack
    console.log('Slack notification would be sent:', { alert, config });
  }

  /**
   * Obter nome do provider
   */
  private async getProviderName(providerId: string): Promise<string> {
    // TODO: Implementar busca do nome do provider
    return `Provider ${providerId}`;
  }

  /**
   * Configurar canais de notificação
   */
  setNotificationChannels(channels: NotificationChannel[]): void {
    this.notificationChannels = channels;
  }

  /**
   * Obter alertas ativos
   */
  getActiveAlerts(): Alert[] {
    return Array.from(this.alerts.values()).filter(alert => !alert.resolved);
  }

  /**
   * Obter todos os alertas
   */
  getAllAlerts(): Alert[] {
    return Array.from(this.alerts.values());
  }

  /**
   * Resolver alerta manualmente
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId);
    if (alert && !alert.resolved) {
      alert.resolved = true;
      alert.resolvedAt = new Date();
      return true;
    }
    return false;
  }

  /**
   * Limpar alertas antigos
   */
  cleanupOldAlerts(daysOld: number = 7): void {
    const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
    
    for (const [alertId, alert] of this.alerts) {
      if (alert.resolved && alert.resolvedAt && alert.resolvedAt < cutoffDate) {
        this.alerts.delete(alertId);
      }
    }
  }
}

// Instância singleton
export const monitoringSystem = new PaymentProviderMonitoringSystem();
