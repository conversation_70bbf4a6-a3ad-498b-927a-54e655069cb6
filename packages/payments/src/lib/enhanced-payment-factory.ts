import { PaymentProvider } from "@prisma/client";
import { IPaymentProvider, PaymentRequest, PaymentMethod, ProviderHealthStatus } from "../types/payment-provider";
import { BasePaymentProvider } from "./base-provider";
import { paymentProviderManager } from "./provider-manager";

/**
 * Registry de providers disponíveis
 */
class ProviderRegistry {
  private providers = new Map<string, new () => IPaymentProvider>();
  private instances = new Map<string, IPaymentProvider>();

  register(type: string, providerClass: new () => IPaymentProvider): void {
    this.providers.set(type, providerClass);
  }

  async getInstance(type: string, config: PaymentProvider): Promise<IPaymentProvider> {
    const cacheKey = `${type}_${config.id}`;
    
    if (this.instances.has(cacheKey)) {
      return this.instances.get(cacheKey)!;
    }

    const ProviderClass = this.providers.get(type);
    if (!ProviderClass) {
      throw new Error(`Provider type ${type} not registered`);
    }

    const instance = new ProviderClass();
    await instance.configure({
      id: config.id,
      name: config.name,
      type: config.type,
      isActive: config.isActive,
      settings: config.settings as Record<string, any>,
      supportedMethods: [], // TODO: Extrair dos settings
      priority: (config.settings as any).priority || 0,
      maxRetries: (config.settings as any).maxRetries || 3,
      timeout: (config.settings as any).timeout || 30000,
      healthCheckInterval: (config.settings as any).healthCheckInterval || 5,
    });

    this.instances.set(cacheKey, instance);
    return instance;
  }

  clearCache(): void {
    this.instances.clear();
  }
}

/**
 * Strategy para seleção de providers
 */
interface ProviderSelectionStrategy {
  selectProvider(
    providers: PaymentProvider[],
    method: PaymentMethod,
    request: PaymentRequest
  ): Promise<PaymentProvider | null>;
}

/**
 * Estratégia padrão: seleciona por prioridade e saúde
 */
class DefaultSelectionStrategy implements ProviderSelectionStrategy {
  async selectProvider(
    providers: PaymentProvider[],
    method: PaymentMethod,
    request: PaymentRequest
  ): Promise<PaymentProvider | null> {
    // Filtrar providers ativos que suportam o método
    const eligibleProviders = providers.filter(p => {
      const supportedMethods = p.settings as any;
      return p.isActive && supportedMethods.supportedMethods?.[method];
    });

    if (eligibleProviders.length === 0) {
      return null;
    }

    // Ordenar por prioridade e saúde
    const sortedProviders = eligibleProviders.sort((a, b) => {
      const priorityA = (a.settings as any).priority || 0;
      const priorityB = (b.settings as any).priority || 0;
      
      // Primeiro por prioridade
      if (priorityA !== priorityB) {
        return priorityB - priorityA;
      }
      
      // Depois por saúde (providers saudáveis primeiro)
      const healthA = (a.settings as any).isHealthy !== false ? 1 : 0;
      const healthB = (b.settings as any).isHealthy !== false ? 1 : 0;
      
      return healthB - healthA;
    });

    return sortedProviders[0];
  }
}

/**
 * Estratégia de round-robin para balanceamento de carga
 */
class RoundRobinSelectionStrategy implements ProviderSelectionStrategy {
  private counters = new Map<string, number>();

  async selectProvider(
    providers: PaymentProvider[],
    method: PaymentMethod,
    request: PaymentRequest
  ): Promise<PaymentProvider | null> {
    const eligibleProviders = providers.filter(p => {
      const supportedMethods = p.settings as any;
      return p.isActive && supportedMethods.supportedMethods?.[method];
    });

    if (eligibleProviders.length === 0) {
      return null;
    }

    const key = `${method}_${eligibleProviders.map(p => p.id).join('_')}`;
    const currentCount = this.counters.get(key) || 0;
    const selectedIndex = currentCount % eligibleProviders.length;
    
    this.counters.set(key, currentCount + 1);
    
    return eligibleProviders[selectedIndex];
  }
}

/**
 * Factory aprimorado para criação e gerenciamento de providers
 */
export class EnhancedPaymentProviderFactory {
  private registry = new ProviderRegistry();
  private selectionStrategy: ProviderSelectionStrategy = new DefaultSelectionStrategy();
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startHealthCheckScheduler();
  }

  /**
   * Registrar um novo tipo de provider
   */
  registerProvider(type: string, providerClass: new () => IPaymentProvider): void {
    this.registry.register(type, providerClass);
  }

  /**
   * Definir estratégia de seleção de providers
   */
  setSelectionStrategy(strategy: ProviderSelectionStrategy): void {
    this.selectionStrategy = strategy;
  }

  /**
   * Criar pagamento com fallback automático
   */
  async createPaymentWithFallback(
    organizationId: string,
    method: PaymentMethod,
    request: PaymentRequest
  ): Promise<{ response: any; providerId: string } | null> {
    const providers = await paymentProviderManager.getProviderWithFallback(organizationId, method);
    
    if (providers.length === 0) {
      throw new Error(`No providers available for method ${method}`);
    }

    let lastError: Error | null = null;

    for (const providerConfig of providers) {
      try {
        const provider = await this.registry.getInstance(providerConfig.type, providerConfig);
        
        // Verificar saúde do provider antes de usar
        const healthStatus = await provider.healthCheck();
        if (!healthStatus.isHealthy) {
          console.warn(`Provider ${providerConfig.id} is unhealthy, skipping...`);
          continue;
        }

        const response = await provider.createPayment(request);
        
        return {
          response,
          providerId: providerConfig.id
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        // Registrar falha do provider
        await paymentProviderManager.handleProviderFailure(providerConfig.id, lastError);
        
        console.warn(`Provider ${providerConfig.id} failed, trying next...`, lastError.message);
      }
    }

    throw lastError || new Error('All providers failed');
  }

  /**
   * Obter provider específico
   */
  async getProvider(organizationId: string, providerId: string): Promise<IPaymentProvider | null> {
    const providers = await paymentProviderManager.getOrganizationProviders(organizationId);
    const providerConfig = providers.find(p => p.id === providerId);
    
    if (!providerConfig) {
      return null;
    }

    return this.registry.getInstance(providerConfig.type, providerConfig);
  }

  /**
   * Obter provider usando estratégia de seleção
   */
  async getProviderByStrategy(
    organizationId: string,
    method: PaymentMethod,
    request: PaymentRequest
  ): Promise<IPaymentProvider | null> {
    const providers = await paymentProviderManager.getOrganizationProviders(organizationId);
    const selectedProvider = await this.selectionStrategy.selectProvider(providers, method, request);
    
    if (!selectedProvider) {
      return null;
    }

    return this.registry.getInstance(selectedProvider.type, selectedProvider);
  }

  /**
   * Limpar cache de instâncias
   */
  clearCache(): void {
    this.registry.clearCache();
  }

  /**
   * Iniciar scheduler de health checks
   */
  private startHealthCheckScheduler(): void {
    // Executar health checks a cada 5 minutos
    this.healthCheckInterval = setInterval(async () => {
      try {
        await paymentProviderManager.performHealthChecks();
      } catch (error) {
        console.error('Health check scheduler error:', error);
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Parar scheduler de health checks
   */
  stopHealthCheckScheduler(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * Destruir factory e limpar recursos
   */
  destroy(): void {
    this.stopHealthCheckScheduler();
    this.clearCache();
  }
}

// Instância singleton
export const enhancedPaymentFactory = new EnhancedPaymentProviderFactory();

// Estratégias disponíveis
export const SelectionStrategies = {
  Default: DefaultSelectionStrategy,
  RoundRobin: RoundRobinSelectionStrategy,
};
