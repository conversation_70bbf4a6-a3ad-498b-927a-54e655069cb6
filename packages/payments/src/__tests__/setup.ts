import { vi } from 'vitest';

// Mock global fetch
global.fetch = vi.fn();

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock setTimeout and clearTimeout for testing timers
vi.stubGlobal('setTimeout', vi.fn((fn, delay) => {
  if (typeof fn === 'function') {
    fn();
  }
  return 1;
}));

vi.stubGlobal('clearTimeout', vi.fn());

vi.stubGlobal('setInterval', vi.fn((fn, delay) => {
  return 1;
}));

vi.stubGlobal('clearInterval', vi.fn());

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
});
