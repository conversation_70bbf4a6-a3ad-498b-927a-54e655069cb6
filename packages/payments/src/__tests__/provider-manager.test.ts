import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PaymentProviderManagerImpl } from '../lib/provider-manager';
import { PaymentProvider } from '@prisma/client';

// Mock das dependências
vi.mock('@repo/database', () => ({
  getPaymentProvidersByOrganization: vi.fn(),
  createPaymentProvider: vi.fn(),
  setDefaultPixProvider: vi.fn(),
  setDefaultCreditCardProvider: vi.fn(),
  getActivePixProvider: vi.fn(),
  getActiveCreditCardProvider: vi.fn(),
  validatePaymentProviderConfig: vi.fn(),
}));

describe('PaymentProviderManager', () => {
  let providerManager: PaymentProviderManagerImpl;
  const mockOrganizationId = 'org_123';

  beforeEach(() => {
    providerManager = new PaymentProviderManagerImpl();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getAvailableProviders', () => {
    it('should return PIX providers for pix method', () => {
      const providers = providerManager.getAvailableProviders('pix');
      
      expect(providers).toHaveLength(3);
      expect(providers.map(p => p.id)).toContain('pluggou');
      expect(providers.map(p => p.id)).toContain('celcoin');
      expect(providers.map(p => p.id)).toContain('asaas');
    });

    it('should return credit card providers for creditCard method', () => {
      const providers = providerManager.getAvailableProviders('creditCard');
      
      expect(providers).toHaveLength(3);
      expect(providers.map(p => p.id)).toContain('stripe');
      expect(providers.map(p => p.id)).toContain('celcoin');
      expect(providers.map(p => p.id)).toContain('asaas');
    });

    it('should return empty array for unsupported method', () => {
      const providers = providerManager.getAvailableProviders('unsupported' as any);
      
      expect(providers).toHaveLength(0);
    });
  });

  describe('getOrganizationProviders', () => {
    it('should return cached providers on second call', async () => {
      const mockProviders: PaymentProvider[] = [
        {
          id: 'provider_1',
          organizationId: mockOrganizationId,
          name: 'Test Provider',
          type: 'pluggou',
          version: '1.0.0',
          settings: { apiKey: 'test' },
          isActive: true,
          priority: 0,
          maxRetries: 3,
          timeout: 30000,
          lastHealthCheck: null,
          isHealthy: true,
          healthCheckInterval: 5,
          lastError: null,
          errorCount: 0,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          averageResponseTime: 0,
          lastTransactionAt: null,
          rateLimitRequests: null,
          rateLimitWindow: null,
          fallbackProviders: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const { getPaymentProvidersByOrganization } = await import('@repo/database');
      vi.mocked(getPaymentProvidersByOrganization).mockResolvedValue(mockProviders);

      // Primeira chamada
      const providers1 = await providerManager.getOrganizationProviders(mockOrganizationId);
      expect(providers1).toEqual(mockProviders);
      expect(getPaymentProvidersByOrganization).toHaveBeenCalledTimes(1);

      // Segunda chamada (deve usar cache)
      const providers2 = await providerManager.getOrganizationProviders(mockOrganizationId);
      expect(providers2).toEqual(mockProviders);
      expect(getPaymentProvidersByOrganization).toHaveBeenCalledTimes(1);
    });
  });

  describe('getProviderWithFallback', () => {
    it('should return providers sorted by priority', async () => {
      const mockProviders: PaymentProvider[] = [
        {
          id: 'provider_1',
          organizationId: mockOrganizationId,
          name: 'Low Priority Provider',
          type: 'pluggou',
          version: '1.0.0',
          settings: { 
            priority: 1,
            supportedMethods: { pix: true }
          },
          isActive: true,
          priority: 1,
          maxRetries: 3,
          timeout: 30000,
          lastHealthCheck: null,
          isHealthy: true,
          healthCheckInterval: 5,
          lastError: null,
          errorCount: 0,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          averageResponseTime: 0,
          lastTransactionAt: null,
          rateLimitRequests: null,
          rateLimitWindow: null,
          fallbackProviders: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'provider_2',
          organizationId: mockOrganizationId,
          name: 'High Priority Provider',
          type: 'celcoin',
          version: '1.0.0',
          settings: { 
            priority: 10,
            supportedMethods: { pix: true }
          },
          isActive: true,
          priority: 10,
          maxRetries: 3,
          timeout: 30000,
          lastHealthCheck: null,
          isHealthy: true,
          healthCheckInterval: 5,
          lastError: null,
          errorCount: 0,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          averageResponseTime: 0,
          lastTransactionAt: null,
          rateLimitRequests: null,
          rateLimitWindow: null,
          fallbackProviders: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const { getPaymentProvidersByOrganization } = await import('@repo/database');
      vi.mocked(getPaymentProvidersByOrganization).mockResolvedValue(mockProviders);

      const providers = await providerManager.getProviderWithFallback(mockOrganizationId, 'pix');
      
      expect(providers).toHaveLength(2);
      expect(providers[0].id).toBe('provider_2'); // High priority first
      expect(providers[1].id).toBe('provider_1'); // Low priority second
    });

    it('should filter out inactive providers', async () => {
      const mockProviders: PaymentProvider[] = [
        {
          id: 'provider_active',
          organizationId: mockOrganizationId,
          name: 'Active Provider',
          type: 'pluggou',
          version: '1.0.0',
          settings: { 
            supportedMethods: { pix: true }
          },
          isActive: true,
          priority: 0,
          maxRetries: 3,
          timeout: 30000,
          lastHealthCheck: null,
          isHealthy: true,
          healthCheckInterval: 5,
          lastError: null,
          errorCount: 0,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          averageResponseTime: 0,
          lastTransactionAt: null,
          rateLimitRequests: null,
          rateLimitWindow: null,
          fallbackProviders: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'provider_inactive',
          organizationId: mockOrganizationId,
          name: 'Inactive Provider',
          type: 'celcoin',
          version: '1.0.0',
          settings: { 
            supportedMethods: { pix: true }
          },
          isActive: false,
          priority: 0,
          maxRetries: 3,
          timeout: 30000,
          lastHealthCheck: null,
          isHealthy: true,
          healthCheckInterval: 5,
          lastError: null,
          errorCount: 0,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          averageResponseTime: 0,
          lastTransactionAt: null,
          rateLimitRequests: null,
          rateLimitWindow: null,
          fallbackProviders: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const { getPaymentProvidersByOrganization } = await import('@repo/database');
      vi.mocked(getPaymentProvidersByOrganization).mockResolvedValue(mockProviders);

      const providers = await providerManager.getProviderWithFallback(mockOrganizationId, 'pix');
      
      expect(providers).toHaveLength(1);
      expect(providers[0].id).toBe('provider_active');
    });
  });

  describe('performHealthChecks', () => {
    it('should return health status for all providers', async () => {
      const mockProviders: PaymentProvider[] = [
        {
          id: 'provider_healthy',
          organizationId: mockOrganizationId,
          name: 'Healthy Provider',
          type: 'pluggou',
          version: '1.0.0',
          settings: {},
          isActive: true,
          priority: 0,
          maxRetries: 3,
          timeout: 30000,
          lastHealthCheck: null,
          isHealthy: true,
          healthCheckInterval: 5,
          lastError: null,
          errorCount: 0,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          averageResponseTime: 0,
          lastTransactionAt: null,
          rateLimitRequests: null,
          rateLimitWindow: null,
          fallbackProviders: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'provider_unhealthy',
          organizationId: mockOrganizationId,
          name: 'Unhealthy Provider',
          type: 'celcoin',
          version: '1.0.0',
          settings: { lastError: 'Connection failed' },
          isActive: true,
          priority: 0,
          maxRetries: 3,
          timeout: 30000,
          lastHealthCheck: null,
          isHealthy: false,
          healthCheckInterval: 5,
          lastError: 'Connection failed',
          errorCount: 5,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          averageResponseTime: 0,
          lastTransactionAt: null,
          rateLimitRequests: null,
          rateLimitWindow: null,
          fallbackProviders: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const { getPaymentProvidersByOrganization } = await import('@repo/database');
      vi.mocked(getPaymentProvidersByOrganization).mockResolvedValue(mockProviders);

      const healthStatuses = await providerManager.performHealthChecks(mockOrganizationId);
      
      expect(healthStatuses.size).toBe(2);
      
      const healthyStatus = healthStatuses.get('provider_healthy');
      expect(healthyStatus?.isHealthy).toBe(true);
      expect(healthyStatus?.errorCount).toBe(0);
      
      const unhealthyStatus = healthStatuses.get('provider_unhealthy');
      expect(unhealthyStatus?.isHealthy).toBe(false);
      expect(unhealthyStatus?.errorCount).toBe(5);
      expect(unhealthyStatus?.lastError).toBe('Connection failed');
    });
  });

  describe('clearProviderCache', () => {
    it('should clear cache for specific organization', async () => {
      const mockProviders: PaymentProvider[] = [];
      const { getPaymentProvidersByOrganization } = await import('@repo/database');
      vi.mocked(getPaymentProvidersByOrganization).mockResolvedValue(mockProviders);

      // Load providers to populate cache
      await providerManager.getOrganizationProviders(mockOrganizationId);
      expect(getPaymentProvidersByOrganization).toHaveBeenCalledTimes(1);

      // Clear cache
      await providerManager.clearProviderCache(mockOrganizationId);

      // Next call should hit database again
      await providerManager.getOrganizationProviders(mockOrganizationId);
      expect(getPaymentProvidersByOrganization).toHaveBeenCalledTimes(2);
    });

    it('should clear all caches when no organization specified', async () => {
      await providerManager.clearProviderCache();
      
      // This test mainly ensures the method doesn't throw
      expect(true).toBe(true);
    });
  });
});
