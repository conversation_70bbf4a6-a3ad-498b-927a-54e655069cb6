import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { EnhancedPaymentProviderFactory, SelectionStrategies } from '../lib/enhanced-payment-factory';
import { PaymentProvider } from '@prisma/client';
import { PaymentRequest } from '../types/payment-provider';

// Mock das dependências
vi.mock('../lib/provider-manager', () => ({
  paymentProviderManager: {
    getProviderWithFallback: vi.fn(),
    getOrganizationProviders: vi.fn(),
    handleProviderFailure: vi.fn(),
  },
}));

// Mock provider class
class MockProvider {
  readonly id = 'mock';
  readonly name = 'Mock Provider';
  readonly type = 'mock';
  readonly version = '1.0.0';

  async configure() {}
  async healthCheck() {
    return {
      isHealthy: true,
      lastCheck: new Date(),
      responseTime: 100,
      errorCount: 0,
    };
  }
  async createPayment(request: PaymentRequest) {
    return {
      id: 'payment_123',
      status: 'approved' as const,
      amount: request.amount,
      currency: request.currency,
      paymentMethod: 'pix',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }
}

describe('EnhancedPaymentProviderFactory', () => {
  let factory: EnhancedPaymentProviderFactory;
  const mockOrganizationId = 'org_123';

  beforeEach(() => {
    factory = new EnhancedPaymentProviderFactory();
    vi.clearAllMocks();
  });

  afterEach(() => {
    factory.destroy();
    vi.restoreAllMocks();
  });

  describe('registerProvider', () => {
    it('should register a new provider type', () => {
      expect(() => {
        factory.registerProvider('mock', MockProvider);
      }).not.toThrow();
    });
  });

  describe('setSelectionStrategy', () => {
    it('should set default selection strategy', () => {
      const strategy = new SelectionStrategies.Default();
      
      expect(() => {
        factory.setSelectionStrategy(strategy);
      }).not.toThrow();
    });

    it('should set round-robin selection strategy', () => {
      const strategy = new SelectionStrategies.RoundRobin();
      
      expect(() => {
        factory.setSelectionStrategy(strategy);
      }).not.toThrow();
    });
  });

  describe('createPaymentWithFallback', () => {
    it('should create payment with first available provider', async () => {
      const mockProviders: PaymentProvider[] = [
        {
          id: 'provider_1',
          organizationId: mockOrganizationId,
          name: 'Mock Provider',
          type: 'mock',
          version: '1.0.0',
          settings: {},
          isActive: true,
          priority: 0,
          maxRetries: 3,
          timeout: 30000,
          lastHealthCheck: null,
          isHealthy: true,
          healthCheckInterval: 5,
          lastError: null,
          errorCount: 0,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          averageResponseTime: 0,
          lastTransactionAt: null,
          rateLimitRequests: null,
          rateLimitWindow: null,
          fallbackProviders: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const mockRequest: PaymentRequest = {
        amount: 10000, // R$ 100,00
        currency: 'BRL',
        customer: {
          name: 'João Silva',
          email: '<EMAIL>',
          document: '12345678901',
        },
        paymentMethod: {
          id: 'pix',
          name: 'PIX',
          type: 'pix',
          enabled: true,
        },
        description: 'Test payment',
      };

      // Register mock provider
      factory.registerProvider('mock', MockProvider);

      const { paymentProviderManager } = await import('../lib/provider-manager');
      vi.mocked(paymentProviderManager.getProviderWithFallback).mockResolvedValue(mockProviders);

      const result = await factory.createPaymentWithFallback(
        mockOrganizationId,
        'pix',
        mockRequest
      );

      expect(result).toBeDefined();
      expect(result?.providerId).toBe('provider_1');
      expect(result?.response.id).toBe('payment_123');
      expect(result?.response.status).toBe('approved');
    });

    it('should throw error when no providers available', async () => {
      const mockRequest: PaymentRequest = {
        amount: 10000,
        currency: 'BRL',
        customer: {
          name: 'João Silva',
          email: '<EMAIL>',
          document: '12345678901',
        },
        paymentMethod: {
          id: 'pix',
          name: 'PIX',
          type: 'pix',
          enabled: true,
        },
      };

      const { paymentProviderManager } = await import('../lib/provider-manager');
      vi.mocked(paymentProviderManager.getProviderWithFallback).mockResolvedValue([]);

      await expect(
        factory.createPaymentWithFallback(mockOrganizationId, 'pix', mockRequest)
      ).rejects.toThrow('No providers available for method pix');
    });

    it('should try fallback providers when first fails', async () => {
      // Mock provider que falha
      class FailingMockProvider extends MockProvider {
        async createPayment() {
          throw new Error('Provider failed');
        }
      }

      // Mock provider que funciona
      class WorkingMockProvider extends MockProvider {
        readonly id = 'working-mock';
        readonly name = 'Working Mock Provider';
      }

      const mockProviders: PaymentProvider[] = [
        {
          id: 'provider_failing',
          organizationId: mockOrganizationId,
          name: 'Failing Provider',
          type: 'failing-mock',
          version: '1.0.0',
          settings: {},
          isActive: true,
          priority: 10, // Higher priority
          maxRetries: 3,
          timeout: 30000,
          lastHealthCheck: null,
          isHealthy: true,
          healthCheckInterval: 5,
          lastError: null,
          errorCount: 0,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          averageResponseTime: 0,
          lastTransactionAt: null,
          rateLimitRequests: null,
          rateLimitWindow: null,
          fallbackProviders: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'provider_working',
          organizationId: mockOrganizationId,
          name: 'Working Provider',
          type: 'working-mock',
          version: '1.0.0',
          settings: {},
          isActive: true,
          priority: 5, // Lower priority
          maxRetries: 3,
          timeout: 30000,
          lastHealthCheck: null,
          isHealthy: true,
          healthCheckInterval: 5,
          lastError: null,
          errorCount: 0,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          averageResponseTime: 0,
          lastTransactionAt: null,
          rateLimitRequests: null,
          rateLimitWindow: null,
          fallbackProviders: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const mockRequest: PaymentRequest = {
        amount: 10000,
        currency: 'BRL',
        customer: {
          name: 'João Silva',
          email: '<EMAIL>',
          document: '12345678901',
        },
        paymentMethod: {
          id: 'pix',
          name: 'PIX',
          type: 'pix',
          enabled: true,
        },
      };

      // Register both providers
      factory.registerProvider('failing-mock', FailingMockProvider);
      factory.registerProvider('working-mock', WorkingMockProvider);

      const { paymentProviderManager } = await import('../lib/provider-manager');
      vi.mocked(paymentProviderManager.getProviderWithFallback).mockResolvedValue(mockProviders);

      const result = await factory.createPaymentWithFallback(
        mockOrganizationId,
        'pix',
        mockRequest
      );

      expect(result).toBeDefined();
      expect(result?.providerId).toBe('provider_working');
      expect(paymentProviderManager.handleProviderFailure).toHaveBeenCalledWith(
        'provider_failing',
        expect.any(Error)
      );
    });
  });

  describe('getProvider', () => {
    it('should return provider instance for valid provider ID', async () => {
      const mockProviders: PaymentProvider[] = [
        {
          id: 'provider_1',
          organizationId: mockOrganizationId,
          name: 'Mock Provider',
          type: 'mock',
          version: '1.0.0',
          settings: {},
          isActive: true,
          priority: 0,
          maxRetries: 3,
          timeout: 30000,
          lastHealthCheck: null,
          isHealthy: true,
          healthCheckInterval: 5,
          lastError: null,
          errorCount: 0,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          averageResponseTime: 0,
          lastTransactionAt: null,
          rateLimitRequests: null,
          rateLimitWindow: null,
          fallbackProviders: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      factory.registerProvider('mock', MockProvider);

      const { paymentProviderManager } = await import('../lib/provider-manager');
      vi.mocked(paymentProviderManager.getOrganizationProviders).mockResolvedValue(mockProviders);

      const provider = await factory.getProvider(mockOrganizationId, 'provider_1');

      expect(provider).toBeDefined();
      expect(provider?.id).toBe('mock');
    });

    it('should return null for non-existent provider ID', async () => {
      const { paymentProviderManager } = await import('../lib/provider-manager');
      vi.mocked(paymentProviderManager.getOrganizationProviders).mockResolvedValue([]);

      const provider = await factory.getProvider(mockOrganizationId, 'non_existent');

      expect(provider).toBeNull();
    });
  });

  describe('clearCache', () => {
    it('should clear provider instances cache', () => {
      expect(() => {
        factory.clearCache();
      }).not.toThrow();
    });
  });

  describe('destroy', () => {
    it('should cleanup resources', () => {
      expect(() => {
        factory.destroy();
      }).not.toThrow();
    });
  });
});
