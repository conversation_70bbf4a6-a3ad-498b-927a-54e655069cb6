// Tipos base para providers de pagamento
export interface PaymentMethod {
  id: string;
  name: string;
  type: 'pix' | 'credit_card' | 'debit_card' | 'boleto' | 'pix_instant' | 'bank_transfer';
  enabled: boolean;
  requires3DS?: boolean;
  installments?: {
    enabled: boolean;
    min: number;
    max: number;
  };
}

export interface PaymentProviderConfig {
  id: string;
  name: string;
  type: string;
  isActive: boolean;
  settings: Record<string, any>;
  supportedMethods: PaymentMethod[];
  webhookUrl?: string;
  webhookSecret?: string;
}

export interface PaymentRequest {
  amount: number; // em centavos
  currency: string;
  customer: {
    id?: string;
    name: string;
    email: string;
    document: string; // CPF/CNPJ
    phone?: string;
  };
  paymentMethod: PaymentMethod;
  installments?: number;
  metadata?: Record<string, any>;
  orderId?: string;
  description?: string;
}

export interface PaymentResponse {
  id: string;
  status: 'pending' | 'processing' | 'approved' | 'rejected' | 'cancelled';
  amount: number;
  currency: string;
  paymentMethod: string;
  transactionId?: string;
  externalId?: string;
  pixCode?: string;
  pixQrCode?: string;
  boletoUrl?: string;
  boletoBarcode?: string;
  installments?: number;
  fees?: {
    total: number;
    provider: number;
    platform: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface RefundRequest {
  paymentId: string;
  amount?: number; // se não informado, reembolsa o valor total
  reason?: string;
}

export interface RefundResponse {
  id: string;
  paymentId: string;
  amount: number;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
}

export interface WebhookEvent {
  id: string;
  type: string;
  data: any;
  provider: string;
  createdAt: Date;
}

// Status de saúde do provider
export interface ProviderHealthStatus {
  isHealthy: boolean;
  lastCheck: Date;
  responseTime: number;
  errorCount: number;
  lastError?: string;
}

// Métricas do provider
export interface ProviderMetrics {
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  averageResponseTime: number;
  uptime: number;
  lastTransactionAt?: Date;
}

// Configuração avançada do provider
export interface AdvancedProviderConfig extends PaymentProviderConfig {
  priority: number; // Para fallback
  maxRetries: number;
  timeout: number;
  rateLimit?: {
    requests: number;
    window: number; // em segundos
  };
  fallbackProviders?: string[];
  healthCheckInterval: number; // em minutos
}

// Interface base que todos os providers devem implementar
export interface IPaymentProvider {
  readonly id: string;
  readonly name: string;
  readonly type: string;
  readonly version: string;

  // Configuração
  configure(config: AdvancedProviderConfig): Promise<void>;
  isConfigured(): boolean;
  getConfiguration(): AdvancedProviderConfig | null;

  // Pagamentos
  createPayment(request: PaymentRequest): Promise<PaymentResponse>;
  getPayment(paymentId: string): Promise<PaymentResponse>;
  cancelPayment(paymentId: string): Promise<PaymentResponse>;

  // Reembolsos
  createRefund(request: RefundRequest): Promise<RefundResponse>;
  getRefund(refundId: string): Promise<RefundResponse>;

  // Webhooks
  handleWebhook(req: Request): Promise<WebhookEvent>;
  validateWebhook(req: Request): Promise<boolean>;

  // Métodos suportados
  getSupportedMethods(): PaymentMethod[];
  isMethodSupported(methodType: string): boolean;

  // Health check e métricas
  healthCheck(): Promise<ProviderHealthStatus>;
  getMetrics(): Promise<ProviderMetrics>;

  // Lifecycle
  initialize(): Promise<void>;
  destroy(): Promise<void>;
}
