# Sistema de Gerenciamento de Providers de Pagamento v2.0

Sistema completo e robusto para gerenciar múltiplos gateways de pagamento com arquitetura escalável, monitoramento avançado e fallback automático.

## 🚀 Funcionalidades v2.0

### ✅ Implementadas

- **Gerenciamento Avançado de Providers**: CRUD completo com cache inteligente
- **Arquitetura Baseada em Interfaces**: Padrão Strategy + Factory + Base Provider
- **Fallback Automático**: Seleção inteligente com múltiplos providers
- **Monitoramento em Tempo Real**: Health checks automáticos e alertas
- **Métricas Detalhadas**: Performance, uptime, taxa de sucesso
- **Interface Web Aprimorada**: Dashboard com métricas e controles avançados
- **APIs REST Completas**: Endpoints para todas as operações
- **Testes Automatizados**: Cobertura completa com Vitest
- **Sistema de Alertas**: Notificações via webhook, email, Slack

### 🔧 Providers Suportados

- **Pluggou**: PIX especializado com QR Code instantâneo
- **Celcoin**: Gateway completo (PIX + Cartão + Boleto + Assinaturas)
- **Stripe**: Pagamentos internacionais (Cartão)
- **ASAAS**: Gateway brasileiro completo (PIX + Cartão + Boleto)

## 🏗️ Arquitetura v2.0

### Principais Melhorias

1. **Interface Base Padronizada** (`IPaymentProvider`)
2. **Provider Base Abstrato** com retry automático e instrumentação
3. **Factory Aprimorado** com estratégias de seleção
4. **Sistema de Monitoramento** com alertas inteligentes
5. **Modelo de Banco Expandido** com métricas e health status
6. **Cache Inteligente** com invalidação automática

### Estrutura do Projeto

```
packages/payments/
├── src/
│   ├── lib/
│   │   ├── base-provider.ts              # Classe base para providers
│   │   ├── provider-manager.ts           # Gerenciador com cache e fallback
│   │   ├── enhanced-payment-factory.ts   # Factory com estratégias
│   │   └── monitoring-system.ts          # Sistema de monitoramento
│   ├── types/
│   │   └── payment-provider.ts           # Interfaces e tipos
│   └── __tests__/                        # Testes automatizados
├── provider/
│   ├── pluggou/
│   │   ├── index.ts                      # Provider original
│   │   └── enhanced-provider.ts          # Provider v2.0
│   ├── celcoin/
│   ├── stripe/
│   └── asaas/
└── database/
    └── prisma/queries/
```

## 🔧 Como Usar v2.0

### 1. Configurar Provider com Fallback

```typescript
import { enhancedPaymentFactory } from "@repo/payments/src/lib/enhanced-payment-factory";
import { EnhancedPluggouProvider } from "@repo/payments/provider/pluggou/enhanced-provider";

// Registrar provider
enhancedPaymentFactory.registerProvider('pluggou', EnhancedPluggouProvider);

// Criar pagamento com fallback automático
const result = await enhancedPaymentFactory.createPaymentWithFallback(
  'org_123',
  'pix',
  {
    amount: 10000, // R$ 100,00
    currency: 'BRL',
    customer: {
      name: 'João Silva',
      email: '<EMAIL>',
      document: '12345678901',
    },
    paymentMethod: {
      id: 'pix',
      name: 'PIX',
      type: 'pix',
      enabled: true,
    },
  }
);
```

### 2. Monitoramento e Alertas

```typescript
import { monitoringSystem } from "@repo/payments/src/lib/monitoring-system";

// Configurar alertas
monitoringSystem.setNotificationChannels([
  {
    type: 'webhook',
    config: { url: 'https://api.example.com/alerts' },
    enabled: true,
  },
]);

// Iniciar monitoramento automático
monitoringSystem.startMonitoring(5); // A cada 5 minutos

// Obter alertas ativos
const activeAlerts = monitoringSystem.getActiveAlerts();
```

### 3. Métricas e Health Checks

```typescript
import { paymentProviderManager } from "@repo/payments/src/lib/provider-manager";

// Executar health check
const healthStatuses = await paymentProviderManager.performHealthChecks('org_123');

// Obter métricas
const metrics = await paymentProviderManager.getProviderMetrics('provider_id');

// Obter providers com fallback
const providers = await paymentProviderManager.getProviderWithFallback('org_123', 'pix');
```

## 📊 Interface Web Aprimorada

### Funcionalidades do Dashboard

- **Métricas em Tempo Real**: Total de transações, taxa de sucesso, tempo médio
- **Status de Saúde**: Indicadores visuais de health status
- **Controles Avançados**: Ativar/desativar, executar health checks
- **Visualização por Abas**: Overview, Performance, Saúde
- **Auto-refresh**: Atualização automática a cada 30 segundos

### Localização

```
apps/web/app/(saas)/app/(organizations)/[organizationSlug]/integrations/payment-providers/
├── page.tsx
└── components/
    └── EnhancedPaymentProvidersClient.tsx
```

## 🔌 APIs REST v2.0

### Endpoints Principais

```
GET    /api/payment-providers?includeMetrics=true&includeHealth=true
POST   /api/payment-providers
PATCH  /api/payment-providers/[id]
DELETE /api/payment-providers/[id]

GET    /api/payment-providers/[id]/health-check
POST   /api/payment-providers/[id]/health-check
GET    /api/payment-providers/[id]/metrics
POST   /api/payment-providers/[id]/metrics/reset

GET    /api/payment-providers/monitoring/alerts
POST   /api/payment-providers/monitoring/alerts/resolve
DELETE /api/payment-providers/monitoring/alerts/cleanup
```

### Exemplo de Resposta com Métricas

```json
{
  "id": "provider_123",
  "name": "Pluggou PIX",
  "type": "pluggou",
  "isActive": true,
  "metrics": {
    "totalTransactions": 1250,
    "successfulTransactions": 1235,
    "failedTransactions": 15,
    "averageResponseTime": 450,
    "uptime": 99.8,
    "successRate": 98.8
  },
  "healthStatus": {
    "isHealthy": true,
    "lastCheck": "2025-09-28T10:30:00Z",
    "responseTime": 420,
    "errorCount": 0
  }
}
```

## 🧪 Testes Automatizados

### Executar Testes

```bash
# Executar todos os testes
npm run test

# Executar com watch mode
npm run test:watch

# Executar com cobertura
npm run test:coverage
```

### Estrutura de Testes

```
src/__tests__/
├── setup.ts                           # Configuração global
├── provider-manager.test.ts           # Testes do gerenciador
├── enhanced-payment-factory.test.ts   # Testes do factory
└── monitoring-system.test.ts          # Testes de monitoramento
```

## 📈 Modelo de Banco v2.0

### Campos Adicionados ao PaymentProvider

```prisma
model PaymentProvider {
  // ... campos existentes
  
  // Configurações avançadas
  version        String       @default("1.0.0")
  priority       Int          @default(0)
  maxRetries     Int          @default(3)
  timeout        Int          @default(30000)
  
  // Health check e métricas
  lastHealthCheck     DateTime?
  isHealthy          Boolean   @default(true)
  healthCheckInterval Int      @default(5)
  lastError          String?
  errorCount         Int       @default(0)
  
  // Métricas de performance
  totalTransactions     Int @default(0)
  successfulTransactions Int @default(0)
  failedTransactions    Int @default(0)
  averageResponseTime   Int @default(0)
  lastTransactionAt     DateTime?
  
  // Rate limiting
  rateLimitRequests Int?
  rateLimitWindow   Int?
  
  // Fallback providers
  fallbackProviders Json?
  
  // Relacionamentos
  transactions Transaction[] @relation("ProviderTransactions")
}
```

## 🚀 Roadmap v3.0

### Próximas Funcionalidades

#### Q1 2025
- [ ] **Machine Learning para Seleção**: Algoritmo inteligente baseado em histórico
- [ ] **Dashboard Analytics**: Gráficos avançados e relatórios
- [ ] **Webhooks Bidirecionais**: Comunicação em tempo real
- [ ] **Multi-tenancy Avançado**: Isolamento completo por organização

#### Q2 2025
- [ ] **Providers Adicionais**: Mercado Pago, PagSeguro, Cielo
- [ ] **Pagamentos Recorrentes**: Sistema completo de assinaturas
- [ ] **Compliance Avançado**: PCI DSS, LGPD, SOX
- [ ] **API GraphQL**: Interface moderna para consultas

#### Q3 2025
- [ ] **Inteligência Artificial**: Detecção de fraudes e otimização
- [ ] **Blockchain Integration**: Suporte a criptomoedas
- [ ] **Global Expansion**: Suporte a providers internacionais
- [ ] **Mobile SDK**: SDKs nativos para iOS e Android

### Melhorias Técnicas

- **Performance**: Cache distribuído com Redis
- **Escalabilidade**: Microserviços com Kubernetes
- **Observabilidade**: OpenTelemetry e Jaeger
- **Segurança**: Vault para secrets, mTLS

## 📞 Suporte e Contribuição

### Como Contribuir

1. Fork o repositório
2. Crie uma branch para sua feature
3. Implemente com testes
4. Abra um Pull Request

### Reportar Issues

Use o template de issue no GitHub com:
- Descrição detalhada
- Steps to reproduce
- Logs relevantes
- Ambiente (OS, Node.js version, etc.)

---

**Versão**: 2.0.0  
**Última Atualização**: 28/09/2025  
**Compatibilidade**: Node.js 18+, TypeScript 5+
