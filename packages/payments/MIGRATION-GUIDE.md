# Guia de Migração v1.0 → v2.0

Este guia ajuda na migração da versão 1.0 para a versão 2.0 do sistema de providers de pagamento.

## 📋 Resumo das Mudanças

### Principais Alterações

1. **Nova Interface Base**: `IPaymentProvider` padronizada
2. **Modelo de Banco Expandido**: Novos campos para métricas e health
3. **Factory Aprimorado**: `EnhancedPaymentProviderFactory`
4. **Sistema de Monitoramento**: Alertas e health checks automáticos
5. **APIs REST Expandidas**: Novos endpoints para métricas

### Breaking Changes

- ❌ `PaymentProviderFactory` → ✅ `EnhancedPaymentProviderFactory`
- ❌ Provider direto → ✅ Provider via `BasePaymentProvider`
- ❌ Configuração simples → ✅ `AdvancedProviderConfig`

## 🗄️ Migração do Banco de Dados

### 1. Executar Migration

```sql
-- Adicionar novos campos ao PaymentProvider
ALTER TABLE payment_providers 
ADD COLUMN version VARCHAR(255) DEFAULT '1.0.0',
ADD COLUMN priority INTEGER DEFAULT 0,
ADD COLUMN max_retries INTEGER DEFAULT 3,
ADD COLUMN timeout INTEGER DEFAULT 30000,
ADD COLUMN last_health_check TIMESTAMP,
ADD COLUMN is_healthy BOOLEAN DEFAULT true,
ADD COLUMN health_check_interval INTEGER DEFAULT 5,
ADD COLUMN last_error TEXT,
ADD COLUMN error_count INTEGER DEFAULT 0,
ADD COLUMN total_transactions INTEGER DEFAULT 0,
ADD COLUMN successful_transactions INTEGER DEFAULT 0,
ADD COLUMN failed_transactions INTEGER DEFAULT 0,
ADD COLUMN average_response_time INTEGER DEFAULT 0,
ADD COLUMN last_transaction_at TIMESTAMP,
ADD COLUMN rate_limit_requests INTEGER,
ADD COLUMN rate_limit_window INTEGER,
ADD COLUMN fallback_providers JSON,
ADD COLUMN payment_provider_id VARCHAR(255);

-- Adicionar índices
CREATE INDEX idx_payment_providers_org_type ON payment_providers(organization_id, type);
CREATE INDEX idx_payment_providers_org_active ON payment_providers(organization_id, is_active);
CREATE INDEX idx_payment_providers_org_priority ON payment_providers(organization_id, priority);

-- Adicionar relacionamento com transactions
ALTER TABLE transactions 
ADD CONSTRAINT fk_transactions_payment_provider 
FOREIGN KEY (payment_provider_id) REFERENCES payment_providers(id);
```

### 2. Migrar Dados Existentes

```typescript
// Script de migração
import { prisma } from '@repo/database';

async function migrateExistingProviders() {
  const providers = await prisma.paymentProvider.findMany();
  
  for (const provider of providers) {
    await prisma.paymentProvider.update({
      where: { id: provider.id },
      data: {
        version: '1.0.0',
        priority: getDefaultPriority(provider.type),
        maxRetries: 3,
        timeout: 30000,
        isHealthy: true,
        healthCheckInterval: 5,
        errorCount: 0,
        totalTransactions: 0,
        successfulTransactions: 0,
        failedTransactions: 0,
        averageResponseTime: 0,
      },
    });
  }
}

function getDefaultPriority(type: string): number {
  const priorities = {
    'pluggou': 10,
    'celcoin': 8,
    'stripe': 6,
    'asaas': 4,
  };
  return priorities[type] || 0;
}
```

## 🔧 Migração do Código

### 1. Atualizar Imports

```typescript
// ❌ Antes (v1.0)
import { PaymentProviderFactory } from "@repo/payments/src/lib/payment-factory";
import { paymentProviderManager } from "@repo/payments/src/lib/provider-manager";

// ✅ Depois (v2.0)
import { EnhancedPaymentProviderFactory } from "@repo/payments/src/lib/enhanced-payment-factory";
import { paymentProviderManager } from "@repo/payments/src/lib/provider-manager";
import { monitoringSystem } from "@repo/payments/src/lib/monitoring-system";
```

### 2. Migrar Factory Usage

```typescript
// ❌ Antes (v1.0)
const checkoutUrl = await PaymentProviderFactory.createCheckoutForMethod(
  organizationId,
  'pix',
  paymentRequest
);

// ✅ Depois (v2.0)
const result = await enhancedPaymentFactory.createPaymentWithFallback(
  organizationId,
  'pix',
  paymentRequest
);
const checkoutUrl = result?.response.checkoutUrl;
```

### 3. Implementar Novos Providers

```typescript
// ❌ Antes (v1.0) - Provider simples
export const createCheckoutLink = async (options) => {
  // Implementação direta
};

// ✅ Depois (v2.0) - Provider baseado em classe
import { BasePaymentProvider } from "@repo/payments/src/lib/base-provider";

export class EnhancedPluggouProvider extends BasePaymentProvider {
  readonly id = 'pluggou';
  readonly name = 'Pluggou';
  readonly type = 'pluggou';
  readonly version = '2.0.0';

  protected async doCreatePayment(request: PaymentRequest): Promise<PaymentResponse> {
    // Implementação com retry automático e instrumentação
  }
  
  // ... outros métodos
}
```

### 4. Registrar Providers

```typescript
// ✅ Novo (v2.0) - Registrar providers
import { enhancedPaymentFactory } from "@repo/payments/src/lib/enhanced-payment-factory";
import { EnhancedPluggouProvider } from "@repo/payments/provider/pluggou/enhanced-provider";

enhancedPaymentFactory.registerProvider('pluggou', EnhancedPluggouProvider);
```

## 🖥️ Migração da Interface Web

### 1. Atualizar Componente

```typescript
// ❌ Antes (v1.0)
import { PaymentProvidersClient } from "./components/PaymentProvidersClient";

// ✅ Depois (v2.0)
import { EnhancedPaymentProvidersClient } from "./components/EnhancedPaymentProvidersClient";

export default function PaymentProvidersPage() {
  return (
    <EnhancedPaymentProvidersClient organizationId={organization.id} />
  );
}
```

### 2. Atualizar APIs

```typescript
// ❌ Antes (v1.0)
const response = await fetch('/api/payment-providers?organizationId=org_123');

// ✅ Depois (v2.0) - Com métricas e health
const response = await fetch('/api/payment-providers?organizationId=org_123&includeMetrics=true&includeHealth=true');
```

## 🔍 Migração do Monitoramento

### 1. Configurar Sistema de Monitoramento

```typescript
// ✅ Novo (v2.0)
import { monitoringSystem } from "@repo/payments/src/lib/monitoring-system";

// Configurar canais de notificação
monitoringSystem.setNotificationChannels([
  {
    type: 'webhook',
    config: { 
      url: process.env.ALERT_WEBHOOK_URL,
      headers: { 'Authorization': `Bearer ${process.env.ALERT_TOKEN}` }
    },
    enabled: true,
  },
]);

// Iniciar monitoramento
monitoringSystem.startMonitoring(5); // A cada 5 minutos
```

### 2. Implementar Health Checks

```typescript
// ✅ Novo (v2.0)
// Health checks automáticos via cron job ou scheduler
import { paymentProviderManager } from "@repo/payments/src/lib/provider-manager";

setInterval(async () => {
  try {
    await paymentProviderManager.performHealthChecks();
  } catch (error) {
    console.error('Health check failed:', error);
  }
}, 5 * 60 * 1000); // A cada 5 minutos
```

## 🧪 Migração dos Testes

### 1. Atualizar Estrutura de Testes

```typescript
// ❌ Antes (v1.0) - Testes manuais
// Sem estrutura de testes

// ✅ Depois (v2.0) - Testes automatizados
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { EnhancedPaymentProviderFactory } from '../lib/enhanced-payment-factory';

describe('EnhancedPaymentProviderFactory', () => {
  let factory: EnhancedPaymentProviderFactory;

  beforeEach(() => {
    factory = new EnhancedPaymentProviderFactory();
  });

  it('should create payment with fallback', async () => {
    // Teste implementado
  });
});
```

### 2. Executar Testes

```bash
# Instalar dependências de teste
npm install --save-dev vitest @vitest/coverage-v8

# Executar testes
npm run test

# Executar com cobertura
npm run test:coverage
```

## ⚠️ Pontos de Atenção

### 1. Compatibilidade com Providers Existentes

- Providers v1.0 continuam funcionando
- Recomendado migrar gradualmente para v2.0
- Usar `BasePaymentProvider` para novos providers

### 2. Performance

- Cache inteligente pode impactar uso de memória
- Monitorar uso de recursos após migração
- Configurar TTL adequado para cache

### 3. Monitoramento

- Alertas podem gerar muito ruído inicialmente
- Ajustar thresholds conforme necessário
- Configurar canais de notificação adequados

## 📝 Checklist de Migração

### Banco de Dados
- [ ] Executar migration SQL
- [ ] Migrar dados existentes
- [ ] Verificar índices criados
- [ ] Testar relacionamentos

### Código
- [ ] Atualizar imports
- [ ] Migrar factory usage
- [ ] Implementar novos providers
- [ ] Registrar providers
- [ ] Atualizar interface web

### Monitoramento
- [ ] Configurar sistema de monitoramento
- [ ] Definir canais de notificação
- [ ] Configurar health checks
- [ ] Ajustar thresholds

### Testes
- [ ] Implementar testes automatizados
- [ ] Configurar CI/CD
- [ ] Verificar cobertura
- [ ] Testar em ambiente de staging

### Deploy
- [ ] Deploy gradual (feature flags)
- [ ] Monitorar métricas pós-deploy
- [ ] Rollback plan preparado
- [ ] Documentação atualizada

## 🆘 Troubleshooting

### Problemas Comuns

1. **Provider não encontrado**
   - Verificar se provider foi registrado
   - Verificar configuração no banco

2. **Health checks falhando**
   - Verificar conectividade de rede
   - Verificar credenciais do provider
   - Ajustar timeout se necessário

3. **Alertas excessivos**
   - Ajustar thresholds
   - Verificar configuração de canais
   - Implementar rate limiting

4. **Performance degradada**
   - Verificar uso de cache
   - Monitorar queries do banco
   - Ajustar configurações de timeout

---

**Suporte**: Para dúvidas sobre migração, abra uma issue no repositório ou entre em contato com a equipe de desenvolvimento.
