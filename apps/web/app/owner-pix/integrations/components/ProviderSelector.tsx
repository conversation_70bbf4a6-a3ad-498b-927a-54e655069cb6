"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
	Select, 
	SelectContent, 
	SelectItem, 
	SelectTrigger, 
	SelectValue 
} from "@/components/ui/select";
import { 
	Smartphone,
	CreditCard,
	CheckCircle,
	AlertCircle
} from "lucide-react";
import { toast } from "sonner";

interface PaymentProvider {
	id: string;
	name: string;
	type: string;
	settings: Record<string, any>;
	isActive: boolean;
	createdAt: string;
	updatedAt: string;
}

interface ProviderSelectorProps {
	providers: PaymentProvider[];
	onProviderChange: (method: "pix" | "creditCard", providerId: string) => void;
}

export function ProviderSelector({ providers, onProviderChange }: ProviderSelectorProps) {
	const [pixProvider, setPixProvider] = useState<string>("");
	const [creditCardProvider, setCreditCardProvider] = useState<string>("");
	const [loading, setLoading] = useState(false);

	// Carregar providers padrão
	useEffect(() => {
		loadDefaultProviders();
	}, []);

	const loadDefaultProviders = async () => {
		try {
			const response = await fetch('/api/payment-providers/default?organizationId=org_123');
			if (response.ok) {
				const data = await response.json();
				if (data.pix) setPixProvider(data.pix.id);
				if (data.creditCard) setCreditCardProvider(data.creditCard.id);
			}
		} catch (error) {
			console.error("Erro ao carregar providers padrão:", error);
		}
	};

	const handleSetDefaultProvider = async (method: "pix" | "creditCard", providerId: string) => {
		if (!providerId) return;

		setLoading(true);
		try {
			const response = await fetch('/api/payment-providers/default', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					organizationId: "org_123", // TODO: Pegar da sessão
					method,
					providerId,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || 'Erro ao definir provider padrão');
			}

			if (method === "pix") {
				setPixProvider(providerId);
			} else {
				setCreditCardProvider(providerId);
			}

			onProviderChange(method, providerId);
			toast.success(`Provider padrão para ${method === "pix" ? "PIX" : "Cartão"} definido com sucesso!`);
		} catch (error) {
			console.error("Erro ao definir provider padrão:", error);
			toast.error(error instanceof Error ? error.message : "Erro ao definir provider padrão");
		} finally {
			setLoading(false);
		}
	};

	const pixProviders = providers.filter(p => p.settings.supportedMethods?.pix && p.isActive);
	const creditCardProviders = providers.filter(p => p.settings.supportedMethods?.creditCard && p.isActive);

	const getProviderIcon = (type: string) => {
		const icons: Record<string, string> = {
			pluggou: "🔗",
			celcoin: "💳",
			asaas: "🏦",
			stripe: "💳",
		};
		return icons[type] || "🔧";
	};

	return (
		<div className="space-y-6">
			<div>
				<h2 className="text-2xl font-bold mb-2">Configuração de Providers</h2>
				<p className="text-muted-foreground">
					Escolha qual provider será usado para cada método de pagamento
				</p>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* Provider PIX */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center space-x-2">
							<Smartphone className="h-5 w-5" />
							<span>Provider PIX</span>
						</CardTitle>
						<CardDescription>
							Escolha o gateway para processar pagamentos PIX
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						{pixProviders.length > 0 ? (
							<>
								<Select
									value={pixProvider}
									onValueChange={(value) => handleSetDefaultProvider("pix", value)}
									disabled={loading}
								>
									<SelectTrigger>
										<SelectValue placeholder="Selecione um provider PIX" />
									</SelectTrigger>
									<SelectContent>
										{pixProviders.map((provider) => (
											<SelectItem key={provider.id} value={provider.id}>
												<div className="flex items-center space-x-2">
													<span>{getProviderIcon(provider.type)}</span>
													<span>{provider.name}</span>
													{provider.id === pixProvider && (
														<CheckCircle className="h-4 w-4 text-green-500" />
													)}
												</div>
											</SelectItem>
										))}
									</SelectContent>
								</Select>

								{pixProvider && (
									<div className="p-3 bg-green-50 rounded-lg">
										<div className="flex items-center space-x-2">
											<CheckCircle className="h-4 w-4 text-green-600" />
											<span className="text-sm font-medium text-green-800">
												Provider PIX ativo
											</span>
										</div>
										<p className="text-sm text-green-700 mt-1">
											{providers.find(p => p.id === pixProvider)?.name}
										</p>
									</div>
								)}
							</>
						) : (
							<div className="p-4 bg-yellow-50 rounded-lg">
								<div className="flex items-center space-x-2">
									<AlertCircle className="h-4 w-4 text-yellow-600" />
									<span className="text-sm font-medium text-yellow-800">
										Nenhum provider PIX configurado
									</span>
								</div>
								<p className="text-sm text-yellow-700 mt-1">
									Configure um provider PIX para processar pagamentos instantâneos
								</p>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Provider Cartão */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center space-x-2">
							<CreditCard className="h-5 w-5" />
							<span>Provider Cartão</span>
						</CardTitle>
						<CardDescription>
							Escolha o gateway para processar cartões de crédito
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						{creditCardProviders.length > 0 ? (
							<>
								<Select
									value={creditCardProvider}
									onValueChange={(value) => handleSetDefaultProvider("creditCard", value)}
									disabled={loading}
								>
									<SelectTrigger>
										<SelectValue placeholder="Selecione um provider de cartão" />
									</SelectTrigger>
									<SelectContent>
										{creditCardProviders.map((provider) => (
											<SelectItem key={provider.id} value={provider.id}>
												<div className="flex items-center space-x-2">
													<span>{getProviderIcon(provider.type)}</span>
													<span>{provider.name}</span>
													{provider.id === creditCardProvider && (
														<CheckCircle className="h-4 w-4 text-green-500" />
													)}
												</div>
											</SelectItem>
										))}
									</SelectContent>
								</Select>

								{creditCardProvider && (
									<div className="p-3 bg-green-50 rounded-lg">
										<div className="flex items-center space-x-2">
											<CheckCircle className="h-4 w-4 text-green-600" />
											<span className="text-sm font-medium text-green-800">
												Provider de cartão ativo
											</span>
										</div>
										<p className="text-sm text-green-700 mt-1">
											{providers.find(p => p.id === creditCardProvider)?.name}
										</p>
									</div>
								)}
							</>
						) : (
							<div className="p-4 bg-yellow-50 rounded-lg">
								<div className="flex items-center space-x-2">
									<AlertCircle className="h-4 w-4 text-yellow-600" />
									<span className="text-sm font-medium text-yellow-800">
										Nenhum provider de cartão configurado
									</span>
								</div>
								<p className="text-sm text-yellow-700 mt-1">
									Configure um provider de cartão para processar pagamentos com cartão
								</p>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			{/* Resumo da configuração */}
			<Card>
				<CardHeader>
					<CardTitle>Resumo da Configuração</CardTitle>
					<CardDescription>
						Configuração atual dos providers de pagamento
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<div className="flex items-center space-x-2">
								<Smartphone className="h-4 w-4" />
								<span className="font-medium">PIX:</span>
								{pixProvider ? (
									<Badge variant="default">
										{providers.find(p => p.id === pixProvider)?.name || "Desconhecido"}
									</Badge>
								) : (
									<Badge variant="secondary">Não configurado</Badge>
								)}
							</div>
						</div>
						<div className="space-y-2">
							<div className="flex items-center space-x-2">
								<CreditCard className="h-4 w-4" />
								<span className="font-medium">Cartão:</span>
								{creditCardProvider ? (
									<Badge variant="default">
										{providers.find(p => p.id === creditCardProvider)?.name || "Desconhecido"}
									</Badge>
								) : (
									<Badge variant="secondary">Não configurado</Badge>
								)}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
