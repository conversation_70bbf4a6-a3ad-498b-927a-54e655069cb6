"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
	Dialog, 
	DialogContent, 
	DialogDescription, 
	DialogHeader, 
	DialogTitle, 
	DialogTrigger 
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { 
	Select, 
	SelectContent, 
	SelectItem, 
	SelectTrigger, 
	SelectValue 
} from "@/components/ui/select";
import { 
	AlertCircle, 
	CheckCircle, 
	Plus, 
	Settings, 
	Trash2, 
	Zap,
	CreditCard,
	Smartphone
} from "lucide-react";
import { toast } from "sonner";
import { ProviderSelector } from "./components/ProviderSelector";

interface PaymentProvider {
	id: string;
	name: string;
	type: string;
	settings: Record<string, any>;
	isActive: boolean;
	createdAt: string;
	updatedAt: string;
}

interface ProviderConfig {
	id: string;
	name: string;
	description: string;
	icon: string;
	supportedMethods: string[];
	requiredSettings: string[];
}

const AVAILABLE_PROVIDERS: Record<string, ProviderConfig[]> = {
	pix: [
		{
			id: "pluggou",
			name: "Pluggou",
			description: "Gateway PIX especializado com QR Code instantâneo",
			icon: "🔗",
			supportedMethods: ["pix"],
			requiredSettings: ["apiKey", "organizationId"],
		},
		{
			id: "celcoin",
			name: "Celcoin",
			description: "Gateway completo com PIX, cartão e boleto",
			icon: "💳",
			supportedMethods: ["pix", "creditCard", "debitCard", "boleto"],
			requiredSettings: ["galaxId", "galaxHash"],
		},
		{
			id: "asaas",
			name: "ASAAS",
			description: "Gateway brasileiro completo",
			icon: "🏦",
			supportedMethods: ["pix", "creditCard", "debitCard", "boleto"],
			requiredSettings: ["apiKey", "environment"],
		},
	],
	creditCard: [
		{
			id: "stripe",
			name: "Stripe",
			description: "Gateway internacional líder mundial",
			icon: "💳",
			supportedMethods: ["creditCard", "debitCard"],
			requiredSettings: ["secretKey", "publishableKey"],
		},
		{
			id: "celcoin",
			name: "Celcoin",
			description: "Gateway completo com PIX, cartão e boleto",
			icon: "💳",
			supportedMethods: ["pix", "creditCard", "debitCard", "boleto"],
			requiredSettings: ["galaxId", "galaxHash"],
		},
		{
			id: "asaas",
			name: "ASAAS",
			description: "Gateway brasileiro completo",
			icon: "🏦",
			supportedMethods: ["pix", "creditCard", "debitCard", "boleto"],
			requiredSettings: ["apiKey", "environment"],
		},
	],
};

export default function IntegrationsPage() {
	const [providers, setProviders] = useState<PaymentProvider[]>([]);
	const [loading, setLoading] = useState(true);
	const [selectedMethod, setSelectedMethod] = useState<"pix" | "creditCard">("pix");
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [selectedProvider, setSelectedProvider] = useState<ProviderConfig | null>(null);
	const [providerSettings, setProviderSettings] = useState<Record<string, string>>({});

	// Carregar providers da organização
	useEffect(() => {
		loadProviders();
	}, []);

	const loadProviders = async () => {
		try {
			setLoading(true);
			const response = await fetch('/api/payment-providers?organizationId=org_123');
			if (!response.ok) {
				throw new Error('Erro ao carregar providers');
			}
			const data = await response.json();
			setProviders(data);
		} catch (error) {
			console.error("Erro ao carregar providers:", error);
			toast.error("Erro ao carregar providers");
			
			// Fallback para dados mock em caso de erro
			setProviders([
				{
					id: "1",
					name: "Pluggou PIX",
					type: "pluggou",
					settings: { 
						apiKey: "pk_live_***", 
						organizationId: "fC99w8SdDGbNJM_q0b2s5",
						supportedMethods: { pix: true, creditCard: false, debitCard: false, boleto: false }
					},
					isActive: true,
					createdAt: "2025-09-28T10:00:00Z",
					updatedAt: "2025-09-28T10:00:00Z",
				},
				{
					id: "2",
					name: "Stripe Cartões",
					type: "stripe",
					settings: { 
						secretKey: "sk_live_***", 
						publishableKey: "pk_live_***",
						supportedMethods: { pix: false, creditCard: true, debitCard: true, boleto: false }
					},
					isActive: true,
					createdAt: "2025-09-28T10:00:00Z",
					updatedAt: "2025-09-28T10:00:00Z",
				},
			]);
		} finally {
			setLoading(false);
		}
	};

	const handleAddProvider = (provider: ProviderConfig) => {
		setSelectedProvider(provider);
		setProviderSettings({});
		setIsDialogOpen(true);
	};

	const handleSaveProvider = async () => {
		if (!selectedProvider) return;

		try {
			const settings = {
				...providerSettings,
				supportedMethods: {
					pix: selectedMethod === "pix",
					creditCard: selectedMethod === "creditCard",
					debitCard: selectedMethod === "creditCard",
					boleto: false,
				}
			};

			const response = await fetch('/api/payment-providers', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					organizationId: "org_123", // TODO: Pegar da sessão
					name: `${selectedProvider.name} ${selectedMethod === "pix" ? "PIX" : "Cartões"}`,
					type: selectedProvider.id,
					settings,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || 'Erro ao salvar provider');
			}

			toast.success("Provider configurado com sucesso!");
			setIsDialogOpen(false);
			loadProviders();
		} catch (error) {
			console.error("Erro ao salvar provider:", error);
			toast.error(error instanceof Error ? error.message : "Erro ao salvar provider");
		}
	};

	const handleToggleProvider = async (providerId: string, isActive: boolean) => {
		try {
			const response = await fetch(`/api/payment-providers/${providerId}`, {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ isActive }),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || 'Erro ao atualizar provider');
			}

			setProviders(prev => 
				prev.map(p => p.id === providerId ? { ...p, isActive } : p)
			);
			toast.success(`Provider ${isActive ? 'ativado' : 'desativado'} com sucesso!`);
		} catch (error) {
			console.error("Erro ao atualizar provider:", error);
			toast.error(error instanceof Error ? error.message : "Erro ao atualizar provider");
		}
	};

	const getProviderIcon = (type: string) => {
		const provider = AVAILABLE_PROVIDERS.pix.concat(AVAILABLE_PROVIDERS.creditCard)
			.find(p => p.id === type);
		return provider?.icon || "🔧";
	};

	const getMethodIcon = (method: string) => {
		return method === "pix" ? <Smartphone className="h-4 w-4" /> : <CreditCard className="h-4 w-4" />;
	};

	if (loading) {
		return (
			<div className="container mx-auto p-6">
				<div className="animate-pulse space-y-4">
					<div className="h-8 bg-gray-200 rounded w-1/4"></div>
					<div className="h-32 bg-gray-200 rounded"></div>
					<div className="h-32 bg-gray-200 rounded"></div>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto p-6 space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Integrações de Pagamento</h1>
					<p className="text-muted-foreground">
						Configure seus gateways de pagamento para PIX e cartões
					</p>
				</div>
			</div>

			{/* Seletor de Providers Padrão */}
			<ProviderSelector 
				providers={providers}
				onProviderChange={(method, providerId) => {
					console.log(`Provider ${method} alterado para:`, providerId);
					// Recarregar providers após mudança
					loadProviders();
				}}
			/>

			<Tabs value={selectedMethod} onValueChange={(value) => setSelectedMethod(value as "pix" | "creditCard")}>
				<TabsList className="grid w-full grid-cols-2">
					<TabsTrigger value="pix" className="flex items-center gap-2">
						<Smartphone className="h-4 w-4" />
						PIX
					</TabsTrigger>
					<TabsTrigger value="creditCard" className="flex items-center gap-2">
						<CreditCard className="h-4 w-4" />
						Cartão de Crédito
					</TabsTrigger>
				</TabsList>

				<TabsContent value="pix" className="space-y-4">
					<div className="grid gap-4">
						{/* Providers configurados para PIX */}
						{providers.filter(p => p.settings.supportedMethods?.pix).map((provider) => (
							<Card key={provider.id}>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<div className="flex items-center space-x-2">
										<span className="text-2xl">{getProviderIcon(provider.type)}</span>
										<div>
											<CardTitle className="text-lg">{provider.name}</CardTitle>
											<CardDescription>
												{AVAILABLE_PROVIDERS.pix.find(p => p.id === provider.type)?.description}
											</CardDescription>
										</div>
									</div>
									<div className="flex items-center space-x-2">
										<Badge variant={provider.isActive ? "default" : "secondary"}>
											{provider.isActive ? "Ativo" : "Inativo"}
										</Badge>
										<Switch
											checked={provider.isActive}
											onCheckedChange={(checked) => handleToggleProvider(provider.id, checked)}
										/>
									</div>
								</CardHeader>
								<CardContent>
									<div className="space-y-2">
										<div className="flex items-center space-x-2">
											<Zap className="h-4 w-4 text-green-500" />
											<span className="text-sm text-green-600">PIX Instantâneo</span>
										</div>
										<div className="text-sm text-muted-foreground">
											Configurado em {new Date(provider.createdAt).toLocaleDateString()}
										</div>
									</div>
								</CardContent>
							</Card>
						))}

						{/* Adicionar novo provider PIX */}
						<Card className="border-dashed">
							<CardHeader>
								<CardTitle className="text-lg">Adicionar Gateway PIX</CardTitle>
								<CardDescription>
									Configure um novo gateway para processar pagamentos PIX
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
									{AVAILABLE_PROVIDERS.pix.map((provider) => (
										<Card key={provider.id} className="cursor-pointer hover:shadow-md transition-shadow">
											<CardHeader className="pb-2">
												<div className="flex items-center space-x-2">
													<span className="text-2xl">{provider.icon}</span>
													<div>
														<CardTitle className="text-base">{provider.name}</CardTitle>
													</div>
												</div>
											</CardHeader>
											<CardContent className="pt-0">
												<p className="text-sm text-muted-foreground mb-3">
													{provider.description}
												</p>
												<Button 
													onClick={() => handleAddProvider(provider)}
													className="w-full"
													size="sm"
												>
													<Plus className="h-4 w-4 mr-2" />
													Configurar
												</Button>
											</CardContent>
										</Card>
									))}
								</div>
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				<TabsContent value="creditCard" className="space-y-4">
					<div className="grid gap-4">
						{/* Providers configurados para Cartão */}
						{providers.filter(p => p.settings.supportedMethods?.creditCard).map((provider) => (
							<Card key={provider.id}>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<div className="flex items-center space-x-2">
										<span className="text-2xl">{getProviderIcon(provider.type)}</span>
										<div>
											<CardTitle className="text-lg">{provider.name}</CardTitle>
											<CardDescription>
												{AVAILABLE_PROVIDERS.creditCard.find(p => p.id === provider.type)?.description}
											</CardDescription>
										</div>
									</div>
									<div className="flex items-center space-x-2">
										<Badge variant={provider.isActive ? "default" : "secondary"}>
											{provider.isActive ? "Ativo" : "Inativo"}
										</Badge>
										<Switch
											checked={provider.isActive}
											onCheckedChange={(checked) => handleToggleProvider(provider.id, checked)}
										/>
									</div>
								</CardHeader>
								<CardContent>
									<div className="space-y-2">
										<div className="flex items-center space-x-2">
											<CreditCard className="h-4 w-4 text-blue-500" />
											<span className="text-sm text-blue-600">Cartão de Crédito</span>
										</div>
										<div className="text-sm text-muted-foreground">
											Configurado em {new Date(provider.createdAt).toLocaleDateString()}
										</div>
									</div>
								</CardContent>
							</Card>
						))}

						{/* Adicionar novo provider Cartão */}
						<Card className="border-dashed">
							<CardHeader>
								<CardTitle className="text-lg">Adicionar Gateway Cartão</CardTitle>
								<CardDescription>
									Configure um novo gateway para processar cartões de crédito
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
									{AVAILABLE_PROVIDERS.creditCard.map((provider) => (
										<Card key={provider.id} className="cursor-pointer hover:shadow-md transition-shadow">
											<CardHeader className="pb-2">
												<div className="flex items-center space-x-2">
													<span className="text-2xl">{provider.icon}</span>
													<div>
														<CardTitle className="text-base">{provider.name}</CardTitle>
													</div>
												</div>
											</CardHeader>
											<CardContent className="pt-0">
												<p className="text-sm text-muted-foreground mb-3">
													{provider.description}
												</p>
												<Button 
													onClick={() => handleAddProvider(provider)}
													className="w-full"
													size="sm"
												>
													<Plus className="h-4 w-4 mr-2" />
													Configurar
												</Button>
											</CardContent>
										</Card>
									))}
								</div>
							</CardContent>
						</Card>
					</div>
				</TabsContent>
			</Tabs>

			{/* Dialog de configuração */}
			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="max-w-2xl">
					<DialogHeader>
						<DialogTitle className="flex items-center space-x-2">
							<span className="text-2xl">{selectedProvider?.icon}</span>
							<span>Configurar {selectedProvider?.name}</span>
						</DialogTitle>
						<DialogDescription>
							{selectedProvider?.description}
						</DialogDescription>
					</DialogHeader>

					{selectedProvider && (
						<div className="space-y-4">
							<div className="space-y-2">
								<Label htmlFor="name">Nome do Provider</Label>
								<Input
									id="name"
									value={`${selectedProvider.name} ${selectedMethod === "pix" ? "PIX" : "Cartões"}`}
									readOnly
								/>
							</div>

							{selectedProvider.requiredSettings.map((setting) => (
								<div key={setting} className="space-y-2">
									<Label htmlFor={setting}>
										{setting === "apiKey" && "Chave da API"}
										{setting === "organizationId" && "ID da Organização"}
										{setting === "galaxId" && "Galax ID"}
										{setting === "galaxHash" && "Galax Hash"}
										{setting === "secretKey" && "Chave Secreta"}
										{setting === "publishableKey" && "Chave Pública"}
										{setting === "environment" && "Ambiente"}
									</Label>
									<Input
										id={setting}
										type={setting.includes("Key") || setting.includes("Hash") ? "password" : "text"}
										placeholder={`Digite seu ${setting}`}
										value={providerSettings[setting] || ""}
										onChange={(e) => setProviderSettings(prev => ({
											...prev,
											[setting]: e.target.value
										}))}
									/>
								</div>
							))}

							{selectedProvider.id === "pluggou" && (
								<div className="p-4 bg-blue-50 rounded-lg">
									<div className="flex items-center space-x-2 mb-2">
										<AlertCircle className="h-4 w-4 text-blue-600" />
										<span className="text-sm font-medium text-blue-800">Credenciais de Teste</span>
									</div>
									<div className="text-sm text-blue-700 space-y-1">
										<p><strong>API Key:</strong> pk_live_O5UM27iqKxWSOX4YgRLn9KVV53JOT2wR</p>
										<p><strong>Organization ID:</strong> fC99w8SdDGbNJM_q0b2s5</p>
									</div>
								</div>
							)}

							<div className="flex justify-end space-x-2">
								<Button variant="outline" onClick={() => setIsDialogOpen(false)}>
									Cancelar
								</Button>
								<Button onClick={handleSaveProvider}>
									<CheckCircle className="h-4 w-4 mr-2" />
									Salvar Configuração
								</Button>
							</div>
						</div>
					)}
				</DialogContent>
			</Dialog>
		</div>
	);
}
