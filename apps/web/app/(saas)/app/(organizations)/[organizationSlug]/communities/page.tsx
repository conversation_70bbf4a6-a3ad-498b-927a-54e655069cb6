import { db } from "@repo/database";
import { notFound } from "next/navigation";
import { CommunityImage } from "./components/CommunityImage";

interface CommunitiesPageProps {
  params: Promise<{
    organizationSlug: string;
  }>;
}

async function getCommunities(organizationSlug: string) {
  const organization = await db.organization.findUnique({
    where: { slug: organizationSlug },
    select: { id: true },
  });

  if (!organization) {
    return null;
  }

  return await db.community.findMany({
    where: { organizationId: organization.id },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          logo: true,
        },
      },
    },
    orderBy: { createdAt: "desc" },
  });
}

export default async function CommunitiesPage({ params }: CommunitiesPageProps) {
  const { organizationSlug } = await params;
  const communities = await getCommunities(organizationSlug);

  if (!communities) {
    notFound();
  }

  // Debug: Log communities data to see what we have
  console.log('Communities data:', communities.map(c => ({
    id: c.id,
    name: c.name,
    thumbnail: c.thumbnail,
    hasThumbnail: !!c.thumbnail,
    thumbnailType: typeof c.thumbnail,
    thumbnailLength: c.thumbnail?.length || 0
  })));

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Minhas Comunidades</h1>
            <p className="text-gray-600 dark:text-gray-400">Gerencie suas comunidades e acompanhe o desempenho</p>
          </div>
          <a
            href={`/app/${organizationSlug}/communities/new`}
            className="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary/90 text-primary-foreground font-medium rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Nova Comunidade
          </a>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {communities.map((community) => (
            <div key={community.id} className="group bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-200 dark:border-gray-700 hover:border-primary/30 dark:hover:border-primary/50">
              {/* Community Cover Image */}
              <div className="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden">
                <CommunityImage
                  src={community.thumbnail}
                  alt={community.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                
                {/* Status Badge */}
                <div className="absolute top-4 right-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-semibold shadow-lg ${
                    community.isPublic 
                      ? 'bg-green-500 text-white' 
                      : 'bg-orange-500 text-white'
                  }`}>
                    {community.isPublic ? "Pública" : "Privada"}
                  </span>
                </div>

                {/* Access Type Badge */}
                <div className="absolute top-4 left-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-semibold shadow-lg ${
                    community.accessType === 'FREE' 
                      ? 'bg-green-500 text-white'
                      : community.accessType === 'PAID'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-purple-500 text-white'
                  }`}>
                    {community.accessType === 'FREE' ? 'Gratuito' : 
                     community.accessType === 'PAID' ? 'Pago' : 'Convite'}
                  </span>
                </div>
              </div>
              
              {/* Community Info */}
              <div className="p-6">
                <h3 className="font-bold text-xl text-gray-900 dark:text-white mb-2 group-hover:text-primary dark:group-hover:text-primary transition-colors">
                  {community.name}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                  {community.description}
                </p>
                
                {/* Stats */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                    </svg>
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      {community.memberCount?.toLocaleString() || 0} membros
                    </span>
                  </div>
                  {community.maxMembers && (
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${Math.min((community.memberCount || 0) / community.maxMembers * 100, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {Math.round((community.memberCount || 0) / community.maxMembers * 100)}%
                      </span>
                    </div>
                  )}
                </div>
                
                {/* Price */}
                <div className="flex items-center justify-between">
                  {community.priceCents && community.priceCents > 0 ? (
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold text-green-600 dark:text-green-400">
                        R$ {(community.priceCents / 100).toFixed(2)}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {community.billingType === 'MONTHLY' ? '/mês' : 
                         community.billingType === 'YEARLY' ? '/ano' : 'único'}
                      </span>
                    </div>
                  ) : (
                    <span className="text-lg font-bold text-green-600 dark:text-green-400">
                      Gratuito
                    </span>
                  )}
                  
                  {/* Action Button */}
                  <button className="px-4 py-2 bg-primary hover:bg-primary/90 text-primary-foreground text-sm font-medium rounded-lg transition-colors duration-200">
                    Gerenciar
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {communities.length === 0 && (
          <div className="text-center py-16">
            <div className="mx-auto w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-full flex items-center justify-center mb-6">
              <svg className="w-12 h-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
              Nenhuma comunidade criada
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
              Comece criando sua primeira comunidade para conectar pessoas e construir uma comunidade engajada.
            </p>
            <a
              href={`/app/${organizationSlug}/communities/new`}
              className="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary/90 text-primary-foreground font-medium rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Criar Primeira Comunidade
            </a>
          </div>
        )}
      </div>
    </div>
  );
}