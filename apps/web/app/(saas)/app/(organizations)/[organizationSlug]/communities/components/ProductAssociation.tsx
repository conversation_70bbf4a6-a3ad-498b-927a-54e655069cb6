"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Package, Link, ExternalLink } from "lucide-react";
import { cn } from "@ui/lib";

interface ProductAssociationProps {
  productId?: string;
  productName?: string;
  productPrice?: number;
  isActive?: boolean;
  onAssociateProduct?: () => void;
  onViewProduct?: () => void;
  className?: string;
}

export function ProductAssociation({
  productId,
  productName,
  productPrice,
  isActive = false,
  onAssociateProduct,
  onViewProduct,
  className,
}: ProductAssociationProps) {
  if (!productId) {
    return (
      <Card className={cn("border-dashed border-2 border-gray-300 dark:border-gray-600", className)}>
        <CardContent className="p-6 text-center">
          <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Associar Produto
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            As comunidades pagas devem estar associadas a um produto. Quando o cliente compra o produto, ele ganha acesso à comunidade.
          </p>
          <Button onClick={onAssociateProduct} className="w-full">
            <Link className="h-4 w-4 mr-2" />
            Associar Produto
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5 text-green-600" />
          Produto Associado
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              {productName || "Produto"}
            </h4>
            {productPrice && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                R$ {(productPrice / 100).toFixed(2)}
              </p>
            )}
          </div>
          <Badge variant={isActive ? "default" : "secondary"}>
            {isActive ? "Ativo" : "Inativo"}
          </Badge>
        </div>
        
        <div className="text-sm text-gray-600 dark:text-gray-400">
          <p>✅ Cliente ganha acesso à comunidade após compra</p>
          <p>✅ Acesso automático após pagamento aprovado</p>
          <p>✅ Integração com sistema de pagamentos</p>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={onViewProduct}>
            <ExternalLink className="h-4 w-4 mr-2" />
            Ver Produto
          </Button>
          <Button variant="outline" size="sm" onClick={onAssociateProduct}>
            <Link className="h-4 w-4 mr-2" />
            Alterar
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
