import { useState } from "react";
import { useFileUpload } from "@saas/products/hooks/useFileUpload";

export function useCommunityImageUpload() {
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const { uploadFile, isUploading, uploadProgress } = useFileUpload({
    bucket: "checkoutBanners", // Usando bucket que sabemos que funciona
    onSuccess: (url) => {
      console.log("Upload de imagem da comunidade bem-sucedido, URL:", url);
      setUploadedImage(url);
    },
    onError: (error) => {
      console.error("Erro no upload da imagem da comunidade:", error);
    },
  });

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFile = (file: File) => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.error('Apenas arquivos de imagem são permitidos');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      console.error('Arquivo muito grande. Máximo 5MB');
      return;
    }

    uploadFile(file);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const removeImage = () => {
    setUploadedImage(null);
  };

  return {
    uploadedImage,
    dragActive,
    isUploading,
    uploadProgress,
    handleDrag,
    handleDrop,
    handleFileInput,
    removeImage,
  };
}
