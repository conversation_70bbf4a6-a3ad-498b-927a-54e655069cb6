"use client";

import { useState } from "react";

interface CommunityImageProps {
  src?: string | null;
  alt: string;
  className?: string;
}

export function CommunityImage({ src, alt, className = "w-full h-full object-cover" }: CommunityImageProps) {
  const [imageError, setImageError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(src);
  const [fallbackIndex, setFallbackIndex] = useState(0);

  // Lista de imagens de fallback
  const fallbackImages = [
    "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop&auto=format",
    "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop&auto=format",
    "https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=400&h=300&fit=crop&auto=format",
    "https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=300&fit=crop&auto=format",
    "https://images.unsplash.com/photo-1556761175-b413da4baf72?w=400&h=300&fit=crop&auto=format"
  ];

  const handleError = () => {
    console.log('Image failed to load:', currentSrc);
    console.log('Trying fallback image:', fallbackIndex);
    
    if (fallbackIndex < fallbackImages.length - 1) {
      const nextIndex = fallbackIndex + 1;
      setFallbackIndex(nextIndex);
      setCurrentSrc(fallbackImages[nextIndex]);
      setImageError(false);
    } else {
      setImageError(true);
    }
  };

  const handleLoad = () => {
    console.log('Image loaded successfully:', currentSrc);
  };

  // Determinar qual imagem usar
  let displaySrc;
  if (src && !imageError && fallbackIndex === 0) {
    displaySrc = src;
  } else {
    displaySrc = fallbackImages[fallbackIndex];
  }

  return (
    <img
      src={displaySrc}
      alt={alt}
      className={className}
      onError={handleError}
      onLoad={handleLoad}
    />
  );
}
