"use client";

import { useState } from "react";
import { use<PERSON>out<PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Badge } from "@ui/components/badge";
import { UsersIcon, ImageIcon, Users2Icon } from "lucide-react";
import { useCreateCommunity } from "@saas/communities/hooks/useCommunitiesApi";
import { useFileUpload } from "@repo/storage";
 

const createCommunitySchema = z.object({
  name: z.string().min(1, "Nome é obrigatório").max(60, "Nome deve ter no máximo 60 caracteres"),
  description: z.string().min(10, "Descrição deve ter pelo menos 10 caracteres").max(500, "Descrição deve ter no máximo 500 caracteres"),
  thumbnail: z.string().optional(),
  maxMembers: z.number().min(1).optional(),
});

type CreateCommunityFormData = z.infer<typeof createCommunitySchema>;

interface CreateCommunityFormProps {
  organization: {
    id: string;
    name: string;
    slug: string;
  };
  organizationSlug: string;
}

export function CreateCommunityForm({ organization, organizationSlug }: CreateCommunityFormProps) {
  const router = useRouter();
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);

  const createCommunity = useCreateCommunity();
  
  const { uploadFile, isUploading } = useFileUpload({
    bucket: "checkoutBanners",
    onSuccess: (url) => {
      form.setValue("thumbnail", url);
      setThumbnailPreview(url);
      toast.success("Imagem carregada com sucesso!");
    },
    onError: (error) => {
      toast.error(`Erro ao fazer upload: ${error}`);
    },
  });

  const form = useForm<CreateCommunityFormData>({
    resolver: zodResolver(createCommunitySchema),
    defaultValues: {
      maxMembers: undefined,
    },
  });

  const handleThumbnailUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      toast.error("Por favor, selecione uma imagem válida");
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("A imagem deve ter no máximo 5MB");
      return;
    }

    try {
      const filename = `communities/${organization.id}/${Date.now()}-${file.name}`;
      await uploadFile(file, filename);
    } catch (error) {
      console.error("Upload error:", error);
    }
  };

  const removeThumbnail = () => {
    form.setValue("thumbnail", "");
    setThumbnailPreview(null);
  };

  const onSubmit = async (data: CreateCommunityFormData) => {
    try {
      const communityData = {
        ...data,
        organizationId: organization.id,
        // Comunidade é sempre um produto pago
        accessType: "PAID" as const,
      };

      const result = await createCommunity.mutateAsync(communityData);

      toast.success("Comunidade criada com sucesso!");
      router.push(`/app/${organizationSlug}/communities/${result.community.id}`);
    } catch (error) {
      console.error("Create community error:", error);
      toast.error("Erro ao criar comunidade");
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
      <div className="grid gap-8 lg:grid-cols-2">
        {/* Informações Básicas */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UsersIcon className="h-5 w-5" />
              Informações Básicas
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Nome da Comunidade *</Label>
              <Input
                id="name"
                {...form.register("name")}
                placeholder="Ex: Marketing Digital Brasil"
                className={form.formState.errors.name ? "border-red-500" : ""}
              />
              {form.formState.errors.name && (
                <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descrição *</Label>
              <Textarea
                id="description"
                {...form.register("description")}
                placeholder="Descreva o que sua comunidade oferece e quem deve participar..."
                rows={4}
                className={form.formState.errors.description ? "border-red-500" : ""}
              />
              <p className="text-sm text-gray-500">
                {form.watch("description")?.length || 0}/500 caracteres
              </p>
              {form.formState.errors.description && (
                <p className="text-sm text-red-500">{form.formState.errors.description.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxMembers">Limite de Membros (opcional)</Label>
              <Input
                id="maxMembers"
                type="number"
                {...form.register("maxMembers", { valueAsNumber: true })}
                placeholder="Ex: 1000"
                min="1"
              />
              <p className="text-sm text-gray-500">
                Deixe em branco para ilimitado
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Visual */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5" />
              Visual da Comunidade
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label>Imagem da Comunidade</Label>
              {thumbnailPreview ? (
                <div className="space-y-2">
                  <img
                    src={thumbnailPreview}
                    alt="Preview"
                    className="w-full h-32 object-cover rounded-lg border"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={removeThumbnail}
                  >
                    Remover Imagem
                  </Button>
                </div>
              ) : (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <ImageIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                  <p className="text-sm text-gray-500 mb-2">
                    Adicione uma imagem para sua comunidade
                  </p>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleThumbnailUpload}
                    disabled={isUploading}
                    className="hidden"
                    id="thumbnail-upload"
                  />
                  <Label
                    htmlFor="thumbnail-upload"
                    className="cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    {isUploading ? "Enviando..." : "Escolher Imagem"}
                  </Label>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Resumo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users2Icon className="h-5 w-5" />
            Resumo da Comunidade
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-500">Nome</p>
              <p className="text-lg font-semibold">
                {form.watch("name") || "Nome da comunidade"}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-500">Tipo</p>
              <Badge variant="secondary" className="text-sm">
                Comunidade Paga
              </Badge>
            </div>
          </div>
          <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <strong>Nota:</strong> O preço será configurado no produto associado a esta comunidade. 
              Após criar a comunidade, você poderá configurar o preço e outras opções de venda.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Botões */}
      <div className="flex justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          disabled={createCommunity.isPending}
          className="min-w-[120px]"
        >
          {createCommunity.isPending ? "Criando..." : "Criar Comunidade"}
        </Button>
      </div>
    </form>
  );
}