'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Progress } from '@ui/components/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@ui/components/tabs';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Settings, 
  TrendingUp,
  Zap,
  Shield,
  BarChart3
} from 'lucide-react';
import { toast } from 'sonner';

interface PaymentProvider {
  id: string;
  name: string;
  type: string;
  version: string;
  isActive: boolean;
  priority: number;
  settings: Record<string, any>;
  
  // Health metrics
  lastHealthCheck?: string;
  isHealthy: boolean;
  errorCount: number;
  lastError?: string;
  
  // Performance metrics
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  averageResponseTime: number;
  uptime: number;
  lastTransactionAt?: string;
}

interface ProviderMetrics {
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  averageResponseTime: number;
  uptime: number;
  successRate: number;
}

interface EnhancedPaymentProvidersClientProps {
  organizationId: string;
}

export function EnhancedPaymentProvidersClient({ organizationId }: EnhancedPaymentProvidersClientProps) {
  const [providers, setProviders] = useState<PaymentProvider[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProvider, setSelectedProvider] = useState<PaymentProvider | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadProviders();
    
    // Auto-refresh a cada 30 segundos
    const interval = setInterval(loadProviders, 30000);
    return () => clearInterval(interval);
  }, [organizationId]);

  const loadProviders = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/payment-providers?organizationId=${organizationId}`);
      
      if (!response.ok) {
        throw new Error('Erro ao carregar providers');
      }
      
      const data = await response.json();
      setProviders(data);
    } catch (error) {
      console.error('Erro ao carregar providers:', error);
      toast.error('Erro ao carregar providers de pagamento');
      
      // Fallback para dados mock
      setProviders([
        {
          id: '1',
          name: 'Pluggou PIX',
          type: 'pluggou',
          version: '1.0.0',
          isActive: true,
          priority: 10,
          settings: { supportedMethods: { pix: true } },
          isHealthy: true,
          errorCount: 0,
          totalTransactions: 1250,
          successfulTransactions: 1235,
          failedTransactions: 15,
          averageResponseTime: 450,
          uptime: 99.8,
          lastTransactionAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'Stripe Cartões',
          type: 'stripe',
          version: '1.2.1',
          isActive: true,
          priority: 8,
          settings: { supportedMethods: { creditCard: true } },
          isHealthy: true,
          errorCount: 2,
          totalTransactions: 890,
          successfulTransactions: 875,
          failedTransactions: 15,
          averageResponseTime: 680,
          uptime: 99.5,
          lastTransactionAt: new Date().toISOString(),
        },
        {
          id: '3',
          name: 'Celcoin Gateway',
          type: 'celcoin',
          version: '2.0.0',
          isActive: false,
          priority: 5,
          settings: { supportedMethods: { pix: true, creditCard: true } },
          isHealthy: false,
          errorCount: 12,
          lastError: 'Connection timeout',
          totalTransactions: 45,
          successfulTransactions: 33,
          failedTransactions: 12,
          averageResponseTime: 1200,
          uptime: 85.2,
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleProvider = async (providerId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/payment-providers/${providerId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive }),
      });

      if (!response.ok) {
        throw new Error('Erro ao atualizar provider');
      }

      setProviders(prev => 
        prev.map(p => p.id === providerId ? { ...p, isActive } : p)
      );
      
      toast.success(`Provider ${isActive ? 'ativado' : 'desativado'} com sucesso!`);
    } catch (error) {
      console.error('Erro ao atualizar provider:', error);
      toast.error('Erro ao atualizar provider');
    }
  };

  const handleRunHealthCheck = async (providerId: string) => {
    try {
      const response = await fetch(`/api/payment-providers/${providerId}/health-check`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Erro ao executar health check');
      }

      await loadProviders();
      toast.success('Health check executado com sucesso!');
    } catch (error) {
      console.error('Erro ao executar health check:', error);
      toast.error('Erro ao executar health check');
    }
  };

  const getProviderIcon = (type: string) => {
    const icons: Record<string, string> = {
      pluggou: '🔗',
      celcoin: '💳',
      stripe: '💳',
      asaas: '🏦',
    };
    return icons[type] || '🔧';
  };

  const getHealthBadge = (provider: PaymentProvider) => {
    if (!provider.isActive) {
      return <Badge variant="secondary">Inativo</Badge>;
    }
    
    if (provider.isHealthy) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Saudável</Badge>;
    }
    
    return <Badge variant="destructive">Com Problemas</Badge>;
  };

  const calculateSuccessRate = (provider: PaymentProvider): number => {
    if (provider.totalTransactions === 0) return 0;
    return (provider.successfulTransactions / provider.totalTransactions) * 100;
  };

  const getOverallMetrics = (): ProviderMetrics => {
    const activeProviders = providers.filter(p => p.isActive);
    
    return activeProviders.reduce((acc, provider) => ({
      totalTransactions: acc.totalTransactions + provider.totalTransactions,
      successfulTransactions: acc.successfulTransactions + provider.successfulTransactions,
      failedTransactions: acc.failedTransactions + provider.failedTransactions,
      averageResponseTime: (acc.averageResponseTime + provider.averageResponseTime) / 2,
      uptime: (acc.uptime + provider.uptime) / 2,
      successRate: 0, // Calculado depois
    }), {
      totalTransactions: 0,
      successfulTransactions: 0,
      failedTransactions: 0,
      averageResponseTime: 0,
      uptime: 0,
      successRate: 0,
    });
  };

  const overallMetrics = getOverallMetrics();
  overallMetrics.successRate = overallMetrics.totalTransactions > 0 
    ? (overallMetrics.successfulTransactions / overallMetrics.totalTransactions) * 100 
    : 0;

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Métricas Gerais */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total de Transações</p>
                <p className="text-2xl font-bold">{overallMetrics.totalTransactions.toLocaleString()}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Taxa de Sucesso</p>
                <p className="text-2xl font-bold">{overallMetrics.successRate.toFixed(1)}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Tempo Médio</p>
                <p className="text-2xl font-bold">{Math.round(overallMetrics.averageResponseTime)}ms</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Uptime Médio</p>
                <p className="text-2xl font-bold">{overallMetrics.uptime.toFixed(1)}%</p>
              </div>
              <Activity className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Providers */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="health">Saúde</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {providers.map((provider) => (
            <Card key={provider.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getProviderIcon(provider.type)}</span>
                    <div>
                      <CardTitle className="text-lg">{provider.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">
                        {provider.type} v{provider.version} • Prioridade: {provider.priority}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getHealthBadge(provider)}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleToggleProvider(provider.id, !provider.isActive)}
                    >
                      {provider.isActive ? 'Desativar' : 'Ativar'}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Transações</p>
                    <p className="text-lg font-semibold">{provider.totalTransactions}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Taxa de Sucesso</p>
                    <p className="text-lg font-semibold">{calculateSuccessRate(provider).toFixed(1)}%</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Tempo Médio</p>
                    <p className="text-lg font-semibold">{provider.averageResponseTime}ms</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Uptime</p>
                    <p className="text-lg font-semibold">{provider.uptime}%</p>
                  </div>
                </div>
                
                {provider.lastError && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                      <p className="text-sm text-red-800">Último erro: {provider.lastError}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          {providers.map((provider) => (
            <Card key={provider.id}>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>{getProviderIcon(provider.type)}</span>
                  <span>{provider.name}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Taxa de Sucesso</span>
                      <span>{calculateSuccessRate(provider).toFixed(1)}%</span>
                    </div>
                    <Progress value={calculateSuccessRate(provider)} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Uptime</span>
                      <span>{provider.uptime}%</span>
                    </div>
                    <Progress value={provider.uptime} className="h-2" />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <p className="text-2xl font-bold text-green-600">{provider.successfulTransactions}</p>
                      <p className="text-xs text-muted-foreground">Sucessos</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-red-600">{provider.failedTransactions}</p>
                      <p className="text-xs text-muted-foreground">Falhas</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-blue-600">{provider.averageResponseTime}ms</p>
                      <p className="text-xs text-muted-foreground">Tempo Médio</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="health" className="space-y-4">
          {providers.map((provider) => (
            <Card key={provider.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2">
                    <span>{getProviderIcon(provider.type)}</span>
                    <span>{provider.name}</span>
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRunHealthCheck(provider.id)}
                  >
                    <Activity className="h-4 w-4 mr-2" />
                    Verificar Saúde
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Status</span>
                    <div className="flex items-center space-x-2">
                      {provider.isHealthy ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                      )}
                      <span className={provider.isHealthy ? 'text-green-600' : 'text-red-600'}>
                        {provider.isHealthy ? 'Saudável' : 'Com Problemas'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Erros Recentes</span>
                    <span className="text-sm">{provider.errorCount}</span>
                  </div>
                  
                  {provider.lastHealthCheck && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Última Verificação</span>
                      <span className="text-sm text-muted-foreground">
                        {new Date(provider.lastHealthCheck).toLocaleString()}
                      </span>
                    </div>
                  )}
                  
                  {provider.lastTransactionAt && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Última Transação</span>
                      <span className="text-sm text-muted-foreground">
                        {new Date(provider.lastTransactionAt).toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}
