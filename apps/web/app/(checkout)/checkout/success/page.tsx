// apps/web/app/[locale]/(checkout)/checkout/success/page.tsx
import "server-only";
import { db } from '@repo/database';
import { redirect } from 'next/navigation';
import { ProductAccess } from '../components/success/product-access';
import { SuccessHeader } from '../components/success/success-header';
import { UpsellOffers } from '../components/success/upsell-offers';
import { Prisma } from '@prisma/client';
import { createEnrollment } from 'api/modules/courses/procedures';

import { getLocale } from 'next-intl/server';

interface CheckoutSuccessPageProps {
	params: Promise<{
		locale: string;
	}>;
	searchParams: Promise<{
		orderId: string;
	}>;
}

export default async function CheckoutSuccessPage({
	params,
	searchParams,
}: CheckoutSuccessPageProps) {
	// Aguardar os parâmetros antes de acessá-los
	const resolvedSearchParams = await searchParams;
	const resolvedParams = await params;

	if (!resolvedSearchParams.orderId) {
		redirect('/');
	}

	// Obter o locale atual da URL
	const locale = resolvedParams.locale || 'pt';

	// Buscar ordem com dados do produto e upsells
	const order = await db.order.findUnique({
		where: {
			id: resolvedSearchParams.orderId,
			// Modificado para aceitar ordens pendentes também, para casos em que o webhook ainda não foi processado
			status: { in: ['COMPLETED', 'PENDING'] },
		},
		include: {
			product: {
				include: {
					offers: {
						where: {
							active: true,
						},
						orderBy: {
							order: 'asc',
						},
					},
					course: true, // Adicionado para acessar dados do curso
				},
			},
			user: true,
			offerInteractions: {
				include: {
					offer: true,
				},
			},
		},
	});

	if (!order) {
		redirect('/');
	}

	// Check if this is a MAIN order (or null/undefined for legacy orders) and if there are upsells to show
	if ((order.orderType === 'MAIN' || !order.orderType) && order.status === 'COMPLETED') {
		// Get rejected offer IDs
		const rejectedOfferIds = order.offerInteractions
			.filter((interaction: any) => interaction.action === 'REJECTED')
			.map((interaction: any) => interaction.offerId);

		// Get accepted offer IDs
		const acceptedOfferIds = order.offerInteractions
			.filter((interaction: any) => interaction.action === 'ACCEPTED')
			.map((interaction: any) => interaction.offerId);

		// Find the first upsell that hasn't been rejected or accepted
		const nextUpsell = order.product.offers.find(
			(offer: any) =>
				offer.type === 'UPSELL' &&
				offer.active &&
				!rejectedOfferIds.includes(offer.id) &&
				!acceptedOfferIds.includes(offer.id)
		);

		// If there's an upsell available, redirect to it
		if (nextUpsell) {
			redirect(`/upsell/${order.id}/${nextUpsell.id}`);
		}

		// If no upsells but user rejected an upsell, check for downsells
		const hasRejectedUpsell = order.offerInteractions.some(
			(interaction: any) =>
				interaction.action === 'REJECTED' &&
				order.product.offers.some((offer: any) =>
					offer.id === interaction.offerId && offer.type === 'UPSELL'
				)
		);

		if (hasRejectedUpsell) {
			const nextDownsell = order.product.offers.find(
				(offer: any) =>
					offer.type === 'DOWNSELL' &&
					offer.active &&
					!rejectedOfferIds.includes(offer.id) &&
					!acceptedOfferIds.includes(offer.id)
			);

			if (nextDownsell) {
				redirect(`/downsell/${order.id}/${nextDownsell.id}`);
			}
		}
	}

	// Verificar se é um produto do tipo curso e se o status do pedido é pago
	if (
		order.status === 'COMPLETED' &&
		order.product.type === 'COURSE' &&
		order.product.course
	) {
		try {
			// Verificar se já existe matrícula
			const existingEnrollment = await db.courseEnrollment.findUnique({
				where: {
					userId_courseId: {
						userId: order.userId,
						courseId: order.product.course.id,
					},
				},
			});

			// Se não existir matrícula, criar uma
			if (!existingEnrollment) {
				try {
					// Criar matrícula diretamente no banco de dados em vez de usar o procedimento
					await db.courseEnrollment.create({
						data: {
							userId: order.userId,
							courseId: order.product.course.id,
							progress: {},
							lastAccessedAt: new Date(),
						},
					});
					console.log('[SUCCESS_PAGE] Created enrollment for order:', order.id);
				} catch (error) {
					console.error('[SUCCESS_PAGE] Error creating enrollment:', error);
				}
			}

			// Email de confirmação será enviado pelo webhook do Asaas
			console.log('[SUCCESS_PAGE] Email confirmation will be sent by Asaas webhook');
		} catch (error) {
			console.error('[SUCCESS_PAGE] General error in success page:', error);
		}
	}

	return (
		<div className='min-h-screen bg-gradient-to-b from-white to-gray-50/50'>
			<div className='container max-w-2xl py-16'>
				<SuccessHeader order={order} />

				{/* Mostrar acesso ao produto com melhores instruções */}
				<ProductAccess
					product={{
						id: order.product.id,
						title: order.product.title,
						type: order.product.type,
						thumbnail: order.product.thumbnail,
						course: order.product.course,
					}}
					isPaid={order.status === 'COMPLETED'}
				/>

				{/* Mostrar upsells apenas se existirem e não for ordem principal (para evitar duplicação) */}
				{order.orderType !== 'MAIN' && order.product.offers.length > 0 && (
					<UpsellOffers
						offers={order.product.offers
							.filter((offer: any) => offer.type === 'UPSELL')
							.map((offer: any) => ({
								id: offer.id,
								title: offer.title,
								description: offer.description,
								price: Number(offer.price),
								thumbnail: offer.thumbnail,
							}))}
					/>
				)}
			</div>
		</div>
	);
}
