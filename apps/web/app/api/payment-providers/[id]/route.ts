import { NextRequest, NextResponse } from "next/server";
import { 
	getPaymentProviderById,
	updatePaymentProvider,
	deletePaymentProvider 
} from "@repo/database";

// GET /api/payment-providers/[id] - Buscar provider específico
export async function GET(
	request: NextRequest,
	{ params }: { params: { id: string } }
) {
	try {
		const provider = await getPaymentProviderById(params.id);
		
		if (!provider) {
			return NextResponse.json(
				{ error: "Provider não encontrado" },
				{ status: 404 }
			);
		}
		
		return NextResponse.json(provider);
	} catch (error) {
		console.error("Erro ao buscar provider:", error);
		return NextResponse.json(
			{ error: "Erro interno do servidor" },
			{ status: 500 }
		);
	}
}

// PATCH /api/payment-providers/[id] - Atualizar provider
export async function PATCH(
	request: NextRequest,
	{ params }: { params: { id: string } }
) {
	try {
		const body = await request.json();
		const { name, settings, isActive } = body;

		const updateData: any = {};
		if (name !== undefined) updateData.name = name;
		if (settings !== undefined) updateData.settings = settings;
		if (isActive !== undefined) updateData.isActive = isActive;

		const provider = await updatePaymentProvider(params.id, updateData);
		
		return NextResponse.json(provider);
	} catch (error) {
		console.error("Erro ao atualizar provider:", error);
		return NextResponse.json(
			{ error: "Erro interno do servidor" },
			{ status: 500 }
		);
	}
}

// DELETE /api/payment-providers/[id] - Deletar provider
export async function DELETE(
	request: NextRequest,
	{ params }: { params: { id: string } }
) {
	try {
		await deletePaymentProvider(params.id);
		
		return NextResponse.json({ message: "Provider deletado com sucesso" });
	} catch (error) {
		console.error("Erro ao deletar provider:", error);
		return NextResponse.json(
			{ error: "Erro interno do servidor" },
			{ status: 500 }
		);
	}
}
