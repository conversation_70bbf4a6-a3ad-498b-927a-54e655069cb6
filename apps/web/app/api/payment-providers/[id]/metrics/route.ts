import { NextRequest, NextResponse } from "next/server";
import { paymentProviderManager } from "@repo/payments/src/lib/provider-manager";

// GET /api/payment-providers/[id]/metrics - Obter métricas do provider
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const providerId = params.id;
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "24h"; // 1h, 24h, 7d, 30d

    // Obter métricas do provider
    const metrics = await paymentProviderManager.getProviderMetrics(providerId);

    // TODO: Implementar filtro por período quando tivermos dados históricos
    const filteredMetrics = {
      ...metrics,
      period,
      generatedAt: new Date().toISOString(),
    };

    return NextResponse.json(filteredMetrics);
  } catch (error) {
    console.error("Erro ao obter métricas:", error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: "Provider não encontrado" },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}

// POST /api/payment-providers/[id]/metrics/reset - Resetar métricas do provider
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const providerId = params.id;

    // TODO: Implementar reset de métricas no banco de dados
    // Por enquanto, apenas limpar cache
    await paymentProviderManager.clearProviderCache();

    return NextResponse.json({
      providerId,
      message: "Métricas resetadas com sucesso",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Erro ao resetar métricas:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
