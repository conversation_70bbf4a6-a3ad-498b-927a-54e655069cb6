import { NextRequest, NextResponse } from "next/server";
import { paymentProviderManager } from "@repo/payments/src/lib/provider-manager";
import { monitoringSystem } from "@repo/payments/src/lib/monitoring-system";

// POST /api/payment-providers/[id]/health-check - Executar health check manual
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const providerId = params.id;

    // Executar health check específico do provider
    const healthChecks = await paymentProviderManager.performHealthChecks();
    const healthStatus = healthChecks.get(providerId);

    if (!healthStatus) {
      return NextResponse.json(
        { error: "Provider não encontrado ou não pôde ser verificado" },
        { status: 404 }
      );
    }

    // Executar verificação de monitoramento
    await monitoringSystem.performMonitoringCheck();

    return NextResponse.json({
      providerId,
      healthStatus,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Erro ao executar health check:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}

// GET /api/payment-providers/[id]/health-check - Obter último health check
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const providerId = params.id;

    // Obter health checks mais recentes
    const healthChecks = await paymentProviderManager.performHealthChecks();
    const healthStatus = healthChecks.get(providerId);

    if (!healthStatus) {
      return NextResponse.json(
        { error: "Provider não encontrado" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      providerId,
      healthStatus,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Erro ao obter health check:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
