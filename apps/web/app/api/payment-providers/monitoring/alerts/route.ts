import { NextRequest, NextResponse } from "next/server";
import { monitoringSystem } from "@repo/payments/src/lib/monitoring-system";

// GET /api/payment-providers/monitoring/alerts - Listar alertas
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get("activeOnly") === "true";
    const severity = searchParams.get("severity");
    const providerId = searchParams.get("providerId");

    let alerts = activeOnly 
      ? monitoringSystem.getActiveAlerts()
      : monitoringSystem.getAllAlerts();

    // Filtrar por severidade se especificado
    if (severity) {
      alerts = alerts.filter(alert => alert.severity === severity);
    }

    // Filtrar por provider se especificado
    if (providerId) {
      alerts = alerts.filter(alert => alert.providerId === providerId);
    }

    // Ordenar por timestamp (mais recentes primeiro)
    alerts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    return NextResponse.json({
      alerts,
      total: alerts.length,
      activeCount: monitoringSystem.getActiveAlerts().length,
      generatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Erro ao obter alertas:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}

// POST /api/payment-providers/monitoring/alerts/resolve - Resolver alerta
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { alertId } = body;

    if (!alertId) {
      return NextResponse.json(
        { error: "Alert ID é obrigatório" },
        { status: 400 }
      );
    }

    const resolved = monitoringSystem.resolveAlert(alertId);

    if (!resolved) {
      return NextResponse.json(
        { error: "Alerta não encontrado ou já resolvido" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      alertId,
      message: "Alerta resolvido com sucesso",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Erro ao resolver alerta:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}

// DELETE /api/payment-providers/monitoring/alerts/cleanup - Limpar alertas antigos
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const daysOld = parseInt(searchParams.get("daysOld") || "7");

    monitoringSystem.cleanupOldAlerts(daysOld);

    return NextResponse.json({
      message: `Alertas com mais de ${daysOld} dias foram removidos`,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Erro ao limpar alertas:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
