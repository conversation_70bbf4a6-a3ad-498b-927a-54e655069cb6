import { NextRequest, NextResponse } from "next/server";
import { 
	setDefaultPixProvider,
	setDefaultCreditCardProvider,
	getActivePixProvider,
	getActiveCreditCardProvider 
} from "@repo/database";

// POST /api/payment-providers/default - Definir provider padrão
export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { organizationId, method, providerId } = body;

		if (!organizationId || !method || !providerId) {
			return NextResponse.json(
				{ error: "Campos obrigatórios: organizationId, method, providerId" },
				{ status: 400 }
			);
		}

		if (method === "pix") {
			await setDefaultPixProvider(organizationId, providerId);
		} else if (method === "creditCard") {
			await setDefaultCreditCardProvider(organizationId, providerId);
		} else {
			return NextResponse.json(
				{ error: "Método inválido. Use 'pix' ou 'creditCard'" },
				{ status: 400 }
			);
		}

		return NextResponse.json({ 
			message: `Provider padrão para ${method} definido com sucesso` 
		});
	} catch (error) {
		console.error("Erro ao definir provider padrão:", error);
		return NextResponse.json(
			{ error: "Erro interno do servidor" },
			{ status: 500 }
		);
	}
}

// GET /api/payment-providers/default - Buscar providers padrão
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const organizationId = searchParams.get("organizationId");
		const method = searchParams.get("method");

		if (!organizationId) {
			return NextResponse.json(
				{ error: "Organization ID é obrigatório" },
				{ status: 400 }
			);
		}

		let provider = null;
		
		if (method === "pix") {
			provider = await getActivePixProvider(organizationId);
		} else if (method === "creditCard") {
			provider = await getActiveCreditCardProvider(organizationId);
		} else {
			// Retornar ambos se não especificado
			const [pixProvider, creditCardProvider] = await Promise.all([
				getActivePixProvider(organizationId),
				getActiveCreditCardProvider(organizationId),
			]);
			
			return NextResponse.json({
				pix: pixProvider,
				creditCard: creditCardProvider,
			});
		}

		return NextResponse.json(provider);
	} catch (error) {
		console.error("Erro ao buscar provider padrão:", error);
		return NextResponse.json(
			{ error: "Erro interno do servidor" },
			{ status: 500 }
		);
	}
}
