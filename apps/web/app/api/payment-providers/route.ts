import { NextRequest, NextResponse } from "next/server";
import { 
	createPaymentProvider,
	getPaymentProvidersByOrganization,
	type CreatePaymentProviderData 
} from "@repo/database";
import { validateProviderSettings } from "@repo/payments/src/lib/provider-manager";

// GET /api/payment-providers - Listar providers da organização
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const organizationId = searchParams.get("organizationId");

		if (!organizationId) {
			return NextResponse.json(
				{ error: "Organization ID é obrigatório" },
				{ status: 400 }
			);
		}

		const providers = await getPaymentProvidersByOrganization(organizationId);
		
		return NextResponse.json(providers);
	} catch (error) {
		console.error("Erro ao buscar providers:", error);
		return NextResponse.json(
			{ error: "Erro interno do servidor" },
			{ status: 500 }
		);
	}
}

// POST /api/payment-providers - Criar novo provider
export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { organizationId, name, type, settings } = body;

		if (!organizationId || !name || !type || !settings) {
			return NextResponse.json(
				{ error: "Campos obrigatórios: organizationId, name, type, settings" },
				{ status: 400 }
			);
		}

		// Validar configurações do provider
		const validation = validateProviderSettings(type, settings);
		if (!validation.isValid) {
			return NextResponse.json(
				{ 
					error: "Configurações inválidas", 
					details: validation.errors 
				},
				{ status: 400 }
			);
		}

		const providerData: CreatePaymentProviderData = {
			organizationId,
			name,
			type,
			settings,
			isActive: true,
		};

		const provider = await createPaymentProvider(providerData);
		
		return NextResponse.json(provider, { status: 201 });
	} catch (error) {
		console.error("Erro ao criar provider:", error);
		return NextResponse.json(
			{ error: "Erro interno do servidor" },
			{ status: 500 }
		);
	}
}
