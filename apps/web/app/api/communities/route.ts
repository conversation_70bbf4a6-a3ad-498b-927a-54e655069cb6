import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database/prisma/client";
import { z } from "zod";

const createCommunitySchema = z.object({
  organizationId: z.string().min(1),
  name: z.string().min(1).max(60),
  description: z.string().min(10).max(500),
  thumbnail: z.string().optional(),
  maxMembers: z.number().min(1).optional(),
  accessType: z.literal("PAID"),
});

export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get("organizationId");
    const search = searchParams.get("search");
    const status = searchParams.get("status");
    const accessType = searchParams.get("accessType");

    if (!organizationId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await db.member.findFirst({
      where: {
        userId: session.user.id,
        organizationId,
      },
    });

    if (!membership) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Construir filtros
    const where: any = {
      organizationId,
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (status) {
      where.isActive = status === "active";
    }

    if (accessType) {
      where.accessType = accessType;
    }

    const communities = await db.community.findMany({
      where,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            priceCents: true,
            status: true,
          },
        },
        _count: {
          select: {
            members: true,
            posts: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    return NextResponse.json({ communities });
  } catch (error) {
    console.error("Get communities error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createCommunitySchema.parse(body);

    // Verificar se o usuário tem acesso à organização
    const membership = await db.member.findFirst({
      where: {
        userId: session.user.id,
        organizationId: validatedData.organizationId,
      },
    });

    if (!membership) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Gerar slug único
    const generateSlug = (name: string) => {
      return name
        .toLowerCase()
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();
    };

    const slug = generateSlug(validatedData.name);

    // Verificar se slug já existe
    const existingCommunity = await db.community.findFirst({
      where: {
        slug,
        organizationId: validatedData.organizationId,
      },
    });

    if (existingCommunity) {
      return NextResponse.json({ error: "Community with this name already exists" }, { status: 400 });
    }

    // 1. Criar produto tipo COMMUNITY (preço será configurado depois)
    const product = await db.product.create({
      data: {
        organizationId: validatedData.organizationId,
        creatorId: session.user.id,
        name: validatedData.name,
        slug: slug,
        description: validatedData.description,
        shortDescription: validatedData.description.substring(0, 100),
        priceCents: 0, // Preço será configurado depois
        currency: "BRL",
        type: "COMMUNITY",
        status: "DRAFT",
        visibility: "PRIVATE",
        thumbnail: validatedData.thumbnail,
        language: "pt-BR",
        certificate: false,
        downloadable: false,
        checkoutType: "DEFAULT",
        settings: {},
      },
    });

    // 2. Criar comunidade vinculada ao produto
    const community = await db.community.create({
      data: {
        organizationId: validatedData.organizationId,
        productId: product.id,
        name: validatedData.name,
        slug: slug,
        description: validatedData.description,
        shortDescription: validatedData.description.substring(0, 100),
        thumbnail: validatedData.thumbnail,
        isPublic: false,
        isActive: true,
        memberCount: 0,
        maxMembers: validatedData.maxMembers,
        accessType: "PAID",
        // Preço vem do produto, não da comunidade
      },
    });

    return NextResponse.json({ 
      community,
      product,
      message: "Community created successfully" 
    });
  } catch (error) {
    console.error("Create community error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Invalid data", details: error.errors }, { status: 400 });
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
