import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database/prisma/client";
import { z } from "zod";

const updateCommunitySchema = z.object({
  name: z.string().min(1).max(60).optional(),
  description: z.string().min(10).max(500).optional(),
  priceCents: z.number().min(100).optional(),
  thumbnail: z.string().optional(),
  maxMembers: z.number().min(1).optional(),
  isPublic: z.boolean().optional(),
  isActive: z.boolean().optional(),
});

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const community = await db.community.findUnique({
      where: { id },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            priceCents: true,
            status: true,
          },
        },
        _count: {
          select: {
            members: true,
            posts: true,
          },
        },
      },
    });

    if (!community) {
      return NextResponse.json({ error: "Community not found" }, { status: 404 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await db.member.findFirst({
      where: {
        userId: session.user.id,
        organizationId: community.organizationId,
      },
    });

    if (!membership) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    return NextResponse.json({ community });
  } catch (error) {
    console.error("Get community error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = updateCommunitySchema.parse(body);

    // Buscar comunidade
    const community = await db.community.findUnique({
      where: { id },
      include: { product: true },
    });

    if (!community) {
      return NextResponse.json({ error: "Community not found" }, { status: 404 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await db.member.findFirst({
      where: {
        userId: session.user.id,
        organizationId: community.organizationId,
      },
    });

    if (!membership) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Atualizar comunidade
    const updatedCommunity = await db.community.update({
      where: { id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            priceCents: true,
            status: true,
          },
        },
        _count: {
          select: {
            members: true,
            posts: true,
          },
        },
      },
    });

    // Se o preço foi alterado, atualizar o produto também
    if (validatedData.priceCents && community.product) {
      await db.product.update({
        where: { id: community.product.id },
        data: {
          priceCents: validatedData.priceCents,
          updatedAt: new Date(),
        },
      });
    }

    return NextResponse.json({ 
      community: updatedCommunity,
      message: "Community updated successfully" 
    });
  } catch (error) {
    console.error("Update community error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Invalid data", details: error.errors }, { status: 400 });
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Buscar comunidade
    const community = await db.community.findUnique({
      where: { id },
    });

    if (!community) {
      return NextResponse.json({ error: "Community not found" }, { status: 404 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await db.member.findFirst({
      where: {
        userId: session.user.id,
        organizationId: community.organizationId,
      },
    });

    if (!membership) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Deletar comunidade (cascade deletará o produto também)
    await db.community.delete({
      where: { id },
    });

    return NextResponse.json({ 
      success: true,
      message: "Community deleted successfully" 
    });
  } catch (error) {
    console.error("Delete community error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
