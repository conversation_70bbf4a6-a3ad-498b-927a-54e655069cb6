import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export interface Community {
  id: string;
  organizationId: string;
  productId?: string;
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  thumbnail?: string;
  banner?: string;
  isPublic: boolean;
  isActive: boolean;
  memberCount: number;
  maxMembers?: number;
  accessType: "FREE" | "PAID";
  priceCents?: number;
  createdAt: string;
  updatedAt: string;
  organization: {
    id: string;
    name: string;
    slug: string;
  };
  product?: {
    id: string;
    name: string;
    priceCents: number;
    status: string;
  };
  _count?: {
    members: number;
    posts: number;
  };
}

export interface CreateCommunityData {
  organizationId: string;
  name: string;
  description: string;
  thumbnail?: string;
  maxMembers?: number;
  accessType: "PAID";
}

export interface UpdateCommunityData extends Partial<CreateCommunityData> {
  id: string;
}

export interface CommunityFilters {
  search?: string;
  status?: "active" | "inactive" | "archived";
  accessType?: "FREE" | "PAID";
  organizationId?: string;
}

// API Functions
const apiClient = {
  async getCommunities(organizationId: string, filters: CommunityFilters = {}) {
    const params = new URLSearchParams();
    if (filters.search) params.append("search", filters.search);
    if (filters.status) params.append("status", filters.status);
    if (filters.accessType) params.append("accessType", filters.accessType);
    
    const response = await fetch(`/api/communities?organizationId=${organizationId}&${params.toString()}`);
    if (!response.ok) {
      throw new Error("Failed to fetch communities");
    }
    return response.json() as Promise<{ communities: Community[] }>;
  },

  async getCommunity(id: string) {
    const response = await fetch(`/api/communities/${id}`);
    if (!response.ok) {
      throw new Error("Failed to fetch community");
    }
    return response.json() as Promise<{ community: Community }>;
  },

  async createCommunity(data: CreateCommunityData) {
    const response = await fetch("/api/communities", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create community");
    }
    return response.json() as Promise<{ community: Community; product: any }>;
  },

  async updateCommunity(data: UpdateCommunityData) {
    const { id, ...updateData } = data;
    const response = await fetch(`/api/communities/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateData),
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to update community");
    }
    return response.json() as Promise<{ community: Community }>;
  },

  async deleteCommunity(id: string) {
    const response = await fetch(`/api/communities/${id}`, {
      method: "DELETE",
    });
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to delete community");
    }
    return response.json() as Promise<{ success: boolean }>;
  },
};

// Query Keys
export const communityQueryKeys = {
  all: ["communities"] as const,
  lists: () => [...communityQueryKeys.all, "list"] as const,
  list: (organizationId: string, filters?: CommunityFilters) => 
    [...communityQueryKeys.lists(), organizationId, filters] as const,
  details: () => [...communityQueryKeys.all, "detail"] as const,
  detail: (id: string) => [...communityQueryKeys.details(), id] as const,
};

// Hooks
export function useCommunities(organizationId: string, filters: CommunityFilters = {}) {
  return useQuery({
    queryKey: communityQueryKeys.list(organizationId, filters),
    queryFn: () => apiClient.getCommunities(organizationId, filters),
    enabled: !!organizationId,
  });
}

export function useCommunity(id: string) {
  return useQuery({
    queryKey: communityQueryKeys.detail(id),
    queryFn: () => apiClient.getCommunity(id),
    enabled: !!id,
  });
}

export function useCreateCommunity() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.createCommunity,
    onSuccess: (data, variables) => {
      // Invalidate communities list
      queryClient.invalidateQueries({
        queryKey: ["communities", "list", variables.organizationId],
      });

      // Set the new community in cache
      queryClient.setQueryData(communityQueryKeys.detail(data.community.id), data);
    },
    onError: (error: Error) => {
      toast.error(`Erro ao criar comunidade: ${error.message}`);
    },
  });
}

export function useUpdateCommunity() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.updateCommunity,
    onSuccess: (data, variables) => {
      // Invalidate communities list
      queryClient.invalidateQueries({
        queryKey: ["communities", "list"],
      });

      // Update the community in cache
      queryClient.setQueryData(communityQueryKeys.detail(data.community.id), data);
    },
    onError: (error: Error) => {
      toast.error(`Erro ao atualizar comunidade: ${error.message}`);
    },
  });
}

export function useDeleteCommunity() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.deleteCommunity,
    onSuccess: (_, communityId) => {
      // Invalidate communities list
      queryClient.invalidateQueries({
        queryKey: ["communities", "list"],
      });

      // Remove the community from cache
      queryClient.removeQueries({
        queryKey: communityQueryKeys.detail(communityId),
      });
    },
    onError: (error: Error) => {
      toast.error(`Erro ao deletar comunidade: ${error.message}`);
    },
  });
}
